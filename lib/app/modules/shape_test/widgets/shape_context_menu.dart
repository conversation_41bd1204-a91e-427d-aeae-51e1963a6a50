import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/shape_editor_controller.dart';
import '../constants/grid_constants.dart';
import 'shape_icons.dart';
import 'dart:math' as math;

// Convert to StatefulWidget
class ShapeContextMenu extends StatefulWidget {
  final ShapeEditorController controller;
  final VoidCallback onDismiss;

  // Dynamic breakpoints for different screen sizes
  static const double smallScreenBreakpoint = 360.0;
  static const double mediumScreenBreakpoint = 480.0;
  static const double largeScreenBreakpoint = 600.0;

  const ShapeContextMenu({
    super.key,
    required this.controller,
    required this.onDismiss,
  });

  @override
  State<ShapeContextMenu> createState() => _ShapeContextMenuState();
}

class _ShapeContextMenuState extends State<ShapeContextMenu>
    with SingleTickerProviderStateMixin {
  int _currentPageIndex = 0;
  int _totalPages = 1;
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;

  // Dynamic configuration based on screen size
  late int _maxItemsPerPage;
  late bool _useHorizontalLayout;
  late bool _useCompactMode;
  late bool _showIcons;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _updateLayoutConfiguration(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;

    // Determine layout based on screen size
    if (screenWidth < ShapeContextMenu.smallScreenBreakpoint) {
      _useHorizontalLayout = true;
      _useCompactMode = true;
      _maxItemsPerPage = 3; // Very small screens: 3 items per page
      _showIcons = false;
    } else if (screenWidth < ShapeContextMenu.mediumScreenBreakpoint) {
      _useHorizontalLayout = true;
      _useCompactMode = true;
      _maxItemsPerPage = 4; // Small screens: 4 items per page
      _showIcons = false;
    } else if (screenWidth < ShapeContextMenu.largeScreenBreakpoint) {
      _useHorizontalLayout = true;
      _useCompactMode = false;
      _maxItemsPerPage = 5; // Medium screens: 5 items per page
      _showIcons = false;
    } else {
      _useHorizontalLayout = false;
      _useCompactMode = false;
      _maxItemsPerPage = 8; // Large screens: 8 items per page (vertical layout)
      _showIcons = true;
    }

    // Adjust for very short screens
    if (screenHeight < 500) {
      _maxItemsPerPage = math.min(_maxItemsPerPage, 3);
      _useCompactMode = true;
    }
  }

  void _navigateToPage(int pageIndex) {
    if (_currentPageIndex != pageIndex &&
        pageIndex >= 0 &&
        pageIndex < _totalPages) {
      setState(() {
        _currentPageIndex = pageIndex;
      });

      // Animate the transition
      final isForward = pageIndex > _currentPageIndex;
      _slideAnimation = Tween<Offset>(
        begin: Offset(isForward ? 1.0 : -1.0, 0.0),
        end: Offset.zero,
      ).animate(CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ));
      _animationController.forward(from: 0.0);
    }
  }

  @override
  Widget build(BuildContext context) {
    _updateLayoutConfiguration(context);

    // Add post-frame callback to calculate/adjust position after layout
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (widget.controller.isContextMenuVisible.value &&
          widget.controller.contextMenuKey.currentContext != null &&
          context.mounted) {
        _calculateAndSetImprovedMenuPosition(context);
      }
    });

    return Obx(() {
      final Offset currentPosition =
          widget.controller.calculatedContextMenuPosition.value ?? Offset.zero;

      if (!widget.controller.isContextMenuVisible.value ||
          widget.controller.calculatedContextMenuPosition.value == null) {
        return const SizedBox.shrink();
      }

      return Stack(
        children: [
          // Overlay for capturing taps outside the menu
          Positioned.fill(
            child: GestureDetector(
              onTap: widget.onDismiss,
              behavior: HitTestBehavior.translucent,
              child: Container(color: Colors.transparent),
            ),
          ),

          // The actual menu
          Positioned(
            left: currentPosition.dx,
            top: currentPosition.dy,
            child: GestureDetector(
              onTap: () {}, // Prevent taps on menu background closing it
              behavior: HitTestBehavior.opaque,
              child: AnimatedSize(
                key: widget.controller.contextMenuKey,
                duration: const Duration(milliseconds: 150),
                curve: Curves.easeInOut,
                child: Material(
                  elevation: 8,
                  borderRadius: BorderRadius.circular(_useCompactMode ? 12 : 8),
                  clipBehavior: Clip.antiAlias,
                  color: Get.isDarkMode ? Colors.grey[850] : Colors.white,
                  child: Obx(() => _buildDynamicMenu(context)),
                ),
              ),
            ),
          ),
        ],
      );
    });
  }

  Widget _buildDynamicMenu(BuildContext context) {
    final hasSelection = widget.controller.selectedIndices.isNotEmpty;
    final isMultiSelection = widget.controller.selectedIndices.length > 1;
    final isGroupSelected = hasSelection && widget.controller.isGroupSelected();

    // Get all available actions
    final allActions = _getAllAvailableActions(
        context, hasSelection, isMultiSelection, isGroupSelected);

    // Calculate pages dynamically
    final actionChunks = _chunkActions(allActions);
    _totalPages = actionChunks.length;

    // Ensure current page is valid
    if (_currentPageIndex >= _totalPages) {
      _currentPageIndex = 0;
    }

    return _buildMenuContent(context, actionChunks);
  }

  List<List<Widget Function()>> _chunkActions(List<Widget Function()> actions) {
    if (actions.isEmpty) return [[]];

    final chunks = <List<Widget Function()>>[];
    int itemsPerPage = _maxItemsPerPage;

    // Reserve space for navigation if we'll have multiple pages
    if (actions.length > itemsPerPage) {
      itemsPerPage = _maxItemsPerPage - 1; // Leave space for "More" button
    }

    for (int i = 0; i < actions.length; i += itemsPerPage) {
      final chunk = actions.skip(i).take(itemsPerPage).toList();
      chunks.add(chunk);
    }

    return chunks;
  }

  Widget _buildMenuContent(
      BuildContext context, List<List<Widget Function()>> actionChunks) {
    if (actionChunks.isEmpty || _currentPageIndex >= actionChunks.length) {
      return const SizedBox.shrink();
    }

    final currentPageActions = actionChunks[_currentPageIndex];
    final hasMultiplePages = actionChunks.length > 1;

    List<Widget> menuItems = [];

    // Add navigation button at the beginning if not on first page
    if (hasMultiplePages && _currentPageIndex > 0) {
      menuItems.add(_buildNavigationButton(
        icon: Icons.arrow_back_ios,
        onPressed: () => _navigateToPage(_currentPageIndex - 1),
        isCompact: _useCompactMode,
      ));
      if (!_useHorizontalLayout && menuItems.isNotEmpty) {
        menuItems.add(_buildHorizontalDivider());
      }
    }

    // Add current page actions
    for (int i = 0; i < currentPageActions.length; i++) {
      menuItems.add(currentPageActions[i]());

      // Add dividers between items (but not after the last item)
      if (i < currentPageActions.length - 1 ||
          (hasMultiplePages && _currentPageIndex < actionChunks.length - 1)) {
        if (_useHorizontalLayout) {
          menuItems.add(_buildVerticalDivider());
        } else {
          menuItems.add(_buildHorizontalDivider());
        }
      }
    }

    // Add navigation button at the end if not on last page
    if (hasMultiplePages && _currentPageIndex < actionChunks.length - 1) {
      if (_useHorizontalLayout && menuItems.isNotEmpty) {
        menuItems.add(_buildVerticalDivider());
      } else if (!_useHorizontalLayout && menuItems.isNotEmpty) {
        menuItems.add(_buildHorizontalDivider());
      }

      menuItems.add(_buildNavigationButton(
        icon: Icons.arrow_forward_ios,
        onPressed: () => _navigateToPage(_currentPageIndex + 1),
        isCompact: _useCompactMode,
      ));
    }

    Widget content;
    if (_useHorizontalLayout) {
      // Get screen width and calculate maximum available width for the menu
      final screenWidth = MediaQuery.of(context).size.width;
      final EdgeInsets padding = MediaQuery.of(context).padding;
      final double minMargin = _useCompactMode ? 8.0 : 12.0;
      final double safeLeft = math.max(padding.left, minMargin);
      final double safeRight = math.max(padding.right, minMargin);
      final double maxMenuWidth = screenWidth - safeLeft - safeRight;

      content = ConstrainedBox(
        constraints: BoxConstraints(
          maxWidth: maxMenuWidth,
        ),
        child: SingleChildScrollView(
          scrollDirection: Axis.horizontal,
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: menuItems,
          ),
        ),
      );
    } else {
      content = Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: menuItems,
      );
    }

    // Add page indicator for multiple pages
    if (hasMultiplePages) {
      if (_useHorizontalLayout) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(
                vertical: _useCompactMode ? 4.0 : 6.0,
                horizontal: 8.0,
              ),
              child: content,
            ),
            _buildPageIndicator(),
          ],
        );
      } else {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: EdgeInsets.symmetric(
                vertical: _useCompactMode ? 4.0 : 6.0,
                horizontal: 8.0,
              ),
              child: content,
            ),
            const SizedBox(width: 8),
            _buildPageIndicator(),
          ],
        );
      }
    }

    return Padding(
      padding: EdgeInsets.symmetric(
        vertical: _useCompactMode ? 4.0 : 6.0,
        horizontal: 8.0,
      ),
      child: content,
    );
  }

  Widget _buildPageIndicator() {
    return Container(
      padding: const EdgeInsets.all(4.0),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: List.generate(_totalPages, (index) {
          return Container(
            margin: const EdgeInsets.symmetric(horizontal: 2.0),
            width: 6,
            height: 6,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: index == _currentPageIndex
                  ? (Get.isDarkMode ? Colors.white : Colors.black87)
                  : (Get.isDarkMode ? Colors.grey[600] : Colors.grey[400]),
            ),
          );
        }),
      ),
    );
  }

  Widget _buildNavigationButton({
    required IconData icon,
    required VoidCallback onPressed,
    required bool isCompact,
  }) {
    final Color textColor = Get.isDarkMode ? Colors.white : Colors.black87;

    return TextButton(
      onPressed: onPressed,
      style: TextButton.styleFrom(
        padding: EdgeInsets.symmetric(
          horizontal: isCompact ? 8.0 : 12.0,
          vertical: isCompact ? 6.0 : 8.0,
        ),
        minimumSize: Size(0, isCompact ? 32 : 36),
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.zero),
        foregroundColor: textColor,
        backgroundColor: Colors.transparent,
      ),
      child: Icon(icon, size: isCompact ? 16 : 20, color: textColor),
    );
  }

  // Improved positioning logic that ensures menu stays within viewport
  void _calculateAndSetImprovedMenuPosition(BuildContext context) {
    if (!widget.controller.isContextMenuVisible.value ||
        widget.controller.initialContextMenuPosition.value == null ||
        widget.controller.contextMenuKey.currentContext == null) {
      return;
    }

    final RenderBox? menuRenderBox =
        widget.controller.contextMenuKey.currentContext!.findRenderObject()
            as RenderBox?;
    if (menuRenderBox == null) return;

    final Size menuSize = menuRenderBox.size;
    final Size screenSize = MediaQuery.of(context).size;
    final Offset initialPosition =
        widget.controller.initialContextMenuPosition.value!;

    // Get safe area insets
    final EdgeInsets padding = MediaQuery.of(context).padding;
    final EdgeInsets viewInsets = MediaQuery.of(context).viewInsets;

    // Define minimum margins from screen edges
    final double minMargin = _useCompactMode ? 8.0 : 12.0;
    final double safeLeft = math.max(padding.left, minMargin);
    final double safeTop = math.max(padding.top, minMargin);
    final double safeRight = math.max(padding.right, minMargin);
    final double safeBottom =
        math.max(padding.bottom + viewInsets.bottom, minMargin);

    // Calculate available space
    final double availableWidth = screenSize.width - safeLeft - safeRight;
    final double availableHeight = screenSize.height - safeTop - safeBottom;

    double finalLeft = initialPosition.dx;
    double finalTop = initialPosition.dy;

    // Enhanced horizontal positioning with guaranteed viewport fitting
    if (_useHorizontalLayout) {
      // For horizontal layout, ensure the menu always fits within the screen width
      final double constrainedMenuWidth =
          math.min(menuSize.width, availableWidth);

      // Try to place the menu at the initial position first
      if (finalLeft + constrainedMenuWidth <= screenSize.width - safeRight) {
        // Menu fits at initial position
        finalLeft = math.max(finalLeft, safeLeft);
      } else if (initialPosition.dx - constrainedMenuWidth >= safeLeft) {
        // Try placing to the left of the tap point
        finalLeft = initialPosition.dx - constrainedMenuWidth;
      } else {
        // Center the menu horizontally if neither left nor right placement works optimally
        finalLeft = (screenSize.width - constrainedMenuWidth) / 2;
      }

      // Final clamp to ensure it stays within bounds
      finalLeft = finalLeft.clamp(
          safeLeft, screenSize.width - constrainedMenuWidth - safeRight);
    } else {
      // Original logic for vertical layout
      if (finalLeft + menuSize.width > screenSize.width - safeRight) {
        // Try placing to the left of the tap point
        final double leftPlacement =
            initialPosition.dx - menuSize.width - minMargin;
        if (leftPlacement >= safeLeft) {
          finalLeft = leftPlacement;
        } else {
          // Center horizontally if neither left nor right placement works
          finalLeft = (screenSize.width - menuSize.width) / 2;
          finalLeft = finalLeft.clamp(
              safeLeft, screenSize.width - menuSize.width - safeRight);
        }
      }

      // Ensure minimum left margin
      if (finalLeft < safeLeft) {
        finalLeft = safeLeft;
      }
    }

    // Vertical positioning with smart placement
    if (finalTop + menuSize.height > screenSize.height - safeBottom) {
      // Try placing above the tap point
      final double topPlacement =
          initialPosition.dy - menuSize.height - minMargin;
      if (topPlacement >= safeTop) {
        finalTop = topPlacement;
      } else {
        // If neither above nor below works, try center placement
        final double centerPlacement =
            (screenSize.height - menuSize.height) / 2;
        if (centerPlacement >= safeTop &&
            centerPlacement + menuSize.height <=
                screenSize.height - safeBottom) {
          finalTop = centerPlacement;
        } else {
          // As last resort, clamp to available space
          finalTop = screenSize.height - menuSize.height - safeBottom;
        }
      }
    }

    // Ensure minimum top margin
    if (finalTop < safeTop) {
      finalTop = safeTop;
    }

    // Final bounds check for vertical layout
    if (!_useHorizontalLayout) {
      finalLeft = finalLeft.clamp(
          safeLeft, screenSize.width - menuSize.width - safeRight);
    }
    finalTop = finalTop.clamp(
        safeTop, screenSize.height - menuSize.height - safeBottom);

    final Offset newPosition = Offset(finalLeft, finalTop);

    // Only update if the calculated position actually changed
    if (widget.controller.calculatedContextMenuPosition.value != newPosition) {
      widget.controller.calculatedContextMenuPosition.value = newPosition;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (widget.controller.isContextMenuVisible.value) {
          widget.controller.update();
        }
      });
    }
  }

  // Get all available actions based on current state
  List<Widget Function()> _getAllAvailableActions(
    BuildContext context,
    bool hasSelection,
    bool isMultiSelection,
    bool isGroupSelected,
  ) {
    final textColor = Get.isDarkMode ? Colors.white : Colors.black87;
    final actions = <Widget Function()>[];
    final Set<String> addedActions =
        <String>{}; // Track added actions to prevent duplicates

    // Priority order for actions
    if (!hasSelection) {
      // No selection: Show Paste and Select Objects if available
      if (widget.controller.canPaste()) {
        actions.add(() => _buildMenuItem(
              label: 'shapeEditor_toolbar_actions_paste'.tr,
              icon: Icons.paste,
              onTap: () => _handlePaste(context),
              textColor: textColor,
            ));
        addedActions.add('paste');
      }

      if (widget.controller.shapes.length > 1) {
        actions.add(() => _buildMenuItem(
              label: 'shapeEditor_toolbar_actions_selectObjects'.tr,
              icon: Icons.select_all,
              onTap: () {
                widget.controller.toggleMultiSelectionMode();
                widget.onDismiss();
              },
              textColor: textColor,
            ));
        addedActions.add('select_objects');
      }
    } else {
      // Has selection: Show primary actions first
      if (widget.controller.canPaste()) {
        actions.add(() => _buildMenuItem(
              label: 'shapeEditor_toolbar_actions_paste'.tr,
              icon: Icons.paste,
              onTap: () => _handlePaste(context),
              textColor: textColor,
            ));
        addedActions.add('paste');
      }

      // Core editing actions
      actions.add(() => _buildMenuItem(
            label: 'shapeEditor_toolbar_actions_cut'.tr,
            icon: Icons.cut,
            onTap: () {
              widget.controller.cutSelectedShapes();
              widget.onDismiss();
            },
            textColor: textColor,
          ));
      addedActions.add('cut');

      actions.add(() => _buildMenuItem(
            label: 'shapeEditor_toolbar_actions_copy'.tr,
            icon: Icons.copy,
            onTap: () {
              widget.controller.copySelectedShapes();
              widget.onDismiss();
            },
            textColor: textColor,
          ));
      addedActions.add('copy');

      actions.add(() => _buildMenuItem(
            label: 'shapeEditor_toolbar_actions_duplicate'.tr,
            icon: Icons.control_point_duplicate,
            onTap: () {
              widget.controller.duplicateSelectedShapes();
              widget.onDismiss();
            },
            textColor: textColor,
          ));
      addedActions.add('duplicate');

      // Single shape actions
      if (!isMultiSelection) {
        // Note: Vertex editing is now only accessible via long-press on the green vertex handle
        // The context menu vertex editing option has been removed to consolidate interaction methods
      }

      // Transform actions
      actions.add(() => _buildMenuItem(
            label: 'shapeEditor_toolbar_actions_flipHorizontal'.tr,
            icon: Icons.flip,
            onTap: () {
              widget.controller.flipSelectedShapeHorizontally();
              widget.onDismiss();
            },
            textColor: textColor,
          ));
      addedActions.add('flip_horizontal');

      actions.add(() => _buildMenuItem(
            label: 'shapeEditor_toolbar_actions_flipVertical'.tr,
            icon: Icons.flip,
            onTap: () {
              widget.controller.flipSelectedShapeVertically();
              widget.onDismiss();
            },
            textColor: textColor,
            iconRotation: 1.5708, // 90 degrees
          ));
      addedActions.add('flip_vertical');

      // Group/Ungroup actions
      if (isMultiSelection && widget.controller.canGroupSelectedShapes()) {
        actions.add(() => _buildMenuItem(
              label: 'shapeEditor_toolbar_actions_group'.tr,
              icon: Icons.group_work,
              onTap: () {
                widget.controller.groupSelectedShapes();
                widget.onDismiss();
              },
              textColor: textColor,
            ));
        addedActions.add('group');
      }

      if (isGroupSelected && widget.controller.canUngroupSelectedShape()) {
        actions.add(() => _buildMenuItem(
              label: 'shapeEditor_toolbar_actions_ungroup'.tr,
              icon: Icons.layers_clear,
              onTap: () {
                widget.controller.ungroupSelectedShape();
                widget.onDismiss();
              },
              textColor: textColor,
            ));
        addedActions.add('ungroup');
      }

      // Delete action
      actions.add(() => _buildMenuItem(
            label: 'shapeEditor_toolbar_actions_delete'.tr,
            icon: Icons.delete,
            onTap: () {
              widget.controller.deleteSelectedShapes();
              widget.onDismiss();
            },
            textColor: _useHorizontalLayout ? Colors.red : textColor,
          ));
      addedActions.add('delete');
    }

    // Utility actions (available regardless of selection) - only add if not already added
    if (widget.controller.shapes.length > 1 &&
        !addedActions.contains('select_objects')) {
      actions.add(() => _buildMenuItem(
            label: 'shapeEditor_toolbar_actions_selectObjects'.tr,
            icon: Icons.select_all,
            onTap: () {
              widget.controller.toggleMultiSelectionMode();
              widget.onDismiss();
            },
            textColor: textColor,
          ));
      addedActions.add('select_objects');
    }

    // actions.add(() => _buildMenuItem(
    //       label: widget.controller.isMirrorModeActive.value
    //           ? 'shapeEditor_toolbar_actions_mirrorMode_disable'.tr
    //           : 'shapeEditor_toolbar_actions_mirrorMode_enable'.tr,
    //       icon: Icons.flip,
    //       onTap: () {
    //         widget.controller.toggleMirrorMode();
    //         widget.onDismiss();
    //       },
    //       textColor: textColor,
    //     ));
    // addedActions.add('mirror_mode');

    actions.add(() => _buildMenuItem(
          label: 'shapeEditor_toolbar_actions_resetZoom'.tr,
          icon: Icons.zoom_out_map,
          onTap: () {
            widget.controller.resetZoomAndCenterContent();
            widget.onDismiss();
          },
          textColor: textColor,
          enabled: widget.controller.zoomScale.value != 1.0 ||
              widget.controller.panOffset.value != Offset.zero,
        ));
    addedActions.add('reset_zoom');

    return actions;
  }

  void _handlePaste(BuildContext context) {
    final Offset? screenPosition =
        widget.controller.initialContextMenuPosition.value;
    if (screenPosition != null) {
      final double screenWidth = MediaQuery.of(context).size.width;
      final double baseScreenHeight = MediaQuery.of(context).size.height;
      final double extendedGridHeight =
          GridConstants.getExtendedHeight(baseScreenHeight);
      final Size fullGridSize = Size(screenWidth, extendedGridHeight);

      final Offset gridPosition = widget.controller.gridSystem
          .screenToGridPoint(screenPosition, fullGridSize);
      widget.controller.pasteShapes(gridPosition);
    } else {
      widget.controller.pasteShapes();
    }
    widget.onDismiss();
  }

  Widget _buildMenuItem({
    required String label,
    IconData? icon,
    required VoidCallback onTap,
    required Color textColor,
    bool enabled = true,
    bool isNavigation = false,
    double? iconRotation,
  }) {
    final Color currentTextColor =
        enabled ? textColor : textColor.withOpacity(0.4);
    final Color? specificColor =
        (label.contains('Delete') && _useHorizontalLayout) ? Colors.red : null;
    final Color effectiveTextColor = specificColor ?? currentTextColor;

    final double verticalPadding = _useCompactMode ? 6.0 : 8.0;
    final double horizontalPadding = _useCompactMode ? 8.0 : 12.0;

    // Build child widget with or without icon based on device type
    Widget child;
    if (_showIcons && icon != null) {
      // For tablet devices: show icon on the left of the label
      Widget iconWidget = Icon(
        icon,
        size: _useCompactMode ? 16 : 18,
        color: effectiveTextColor,
      );

      // Apply rotation if specified
      if (iconRotation != null) {
        iconWidget = Transform.rotate(
          angle: iconRotation,
          child: iconWidget,
        );
      }

      child = Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          iconWidget,
          const SizedBox(width: 8),
          Flexible(
            child: Text(
              label,
              style: TextStyle(
                fontSize: _useCompactMode ? 12 : 14,
                fontWeight: FontWeight.w400,
                color: effectiveTextColor,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      );
    } else {
      // For smaller devices: text only
      child = Text(
        label,
        style: TextStyle(
          fontSize: _useCompactMode ? 12 : 14,
          fontWeight: FontWeight.w400,
          color: effectiveTextColor,
        ),
        overflow: TextOverflow.ellipsis,
      );
    }

    return TextButton(
      onPressed: enabled ? onTap : null,
      style: TextButton.styleFrom(
        padding: EdgeInsets.symmetric(
            horizontal: horizontalPadding, vertical: verticalPadding),
        minimumSize: Size(0, _useCompactMode ? 32 : 36),
        tapTargetSize: MaterialTapTargetSize.shrinkWrap,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.zero),
        foregroundColor: effectiveTextColor,
        backgroundColor: Colors.transparent,
        disabledForegroundColor: effectiveTextColor.withOpacity(0.5),
        splashFactory: _useHorizontalLayout
            ? NoSplash.splashFactory
            : InkSplash.splashFactory,
      ),
      child: child,
    );
  }

  Widget _buildHorizontalDivider() {
    final Color dividerColor =
        Get.isDarkMode ? Colors.grey[700]! : Colors.grey[300]!;
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      child: Container(
        height: 1,
        color: dividerColor,
      ),
    );
  }

  Widget _buildVerticalDivider() {
    final Color dividerColor =
        Get.isDarkMode ? Colors.grey[700]! : Colors.grey[300]!;
    return Container(
      height: _useCompactMode ? 16 : 20,
      width: 1,
      color: dividerColor,
      margin: EdgeInsets.symmetric(vertical: _useCompactMode ? 6.0 : 8.0),
    );
  }
}
