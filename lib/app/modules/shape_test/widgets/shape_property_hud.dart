import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'dart:async';
import 'dart:math' as math;
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/shape_test/constants/grid_constants.dart';
import 'package:xoxknit/app/modules/shape_test/widgets/shape_icons.dart';
import 'package:xoxknit/app/modules/shape_test/utils/decimal_input_helper.dart';
import '../controllers/shape_editor_controller.dart';
import '../models/shape_data.dart';
import '../models/group_shape_data.dart';
import '../views/transformable_shape.dart';
import '../utils/geometry_utils.dart';
import '../handlers/shape_manipulation_handlers.dart';
import '../../../utils/dimension_utils.dart'; // Added import

/// Enum for different editing modes in the Property HUD
enum EditingMode {
  defaultSelection, // Normal shape selection and manipulation
  vertexEdit, // Vertex/point editing mode
  edgeEdit, // Edge curve control editing mode
}

/// A HUD (heads-up display) for showing and editing shape properties
class ShapePropertyHUD extends StatefulWidget {
  final ShapeEditorController controller;
  final ShapeData? shapeData; // Make optional
  final Key? shapeKey; // Make optional
  final Size? screenSize; // Make optional

  const ShapePropertyHUD({
    super.key,
    required this.controller,
    this.shapeData, // Make optional
    this.shapeKey, // Make optional
    this.screenSize, // Make optional
  });

  @override
  State<ShapePropertyHUD> createState() => _ShapePropertyHUDState();
}

class _ShapePropertyHUDState extends State<ShapePropertyHUD>
    with SingleTickerProviderStateMixin {
  late TextEditingController _widthController;
  late TextEditingController _heightController;
  late TextEditingController _needleController; // Controller for needle input
  late TextEditingController _rowController; // Controller for row input
  late TextEditingController
      _rotationController; // Controller for rotation input

  late FocusNode _widthFocus;
  late FocusNode _heightFocus;
  late FocusNode _needleFocus; // Focus node for needle input
  late FocusNode _rowFocus; // Focus node for row input
  late FocusNode _rotationFocus; // Focus node for rotation input

  // Remove fade timer
  bool _isEditing = false;

  // Track which field is being actively edited
  bool _isEditingWidth = false;
  bool _isEditingHeight = false;
  bool _isEditingRotation = false;
  bool _isEditingNeedle = false; // Track needle editing state
  bool _isEditingRow = false; // Track row editing state

  // Store original values for history tracking
  String? _originalWidthValue;
  String? _originalHeightValue;
  String? _originalRotationValue;
  String? _originalNeedleValue; // Store original needle value for history
  String? _originalRowValue; // Store original row value for history

  // Error message for dimension constraints
  String? _errorMessage;
  bool _showError = false;

  // For grouped shapes
  bool _isGroupShape = false;

  // State for needle position
  String _needlePositionString = "N/A";
  String? _needlePositionError; // Error message for needle input

  // State for row position
  String _rowPositionString = "N/A";
  String? _rowPositionError; // Error message for row input

  // State for rotation
  String? _rotationError; // Error message for rotation input

  Timer? _debounceTimer; // Timer for debouncing needle input

  /// Consolidated editing mode detection - determines the current editing context
  EditingMode get _currentEditingMode {
    final handleMode = widget.controller.currentHandleMode.value;
    final activeShapeKey = widget.controller.activeHandleModeShapeKey.value;

    // Check if we're editing the current shape
    if (widget.shapeKey != null && activeShapeKey == widget.shapeKey) {
      if (handleMode == HandleMode.pointEdit) {
        return EditingMode.vertexEdit;
      } else if (handleMode == HandleMode.sideEdit) {
        return EditingMode.edgeEdit;
      }
    }

    return EditingMode.defaultSelection;
  }

  /// Check if we're in vertex editing mode (for vertex-specific operations)
  bool get _isInVertexEditMode => _currentEditingMode == EditingMode.vertexEdit;

  /// Check if we're in edge editing mode (for edge-specific operations)
  bool get _isInEdgeEditMode => _currentEditingMode == EditingMode.edgeEdit;

  /// Get the active element being edited (for position control context)
  String get _activeElementDescription {
    switch (_currentEditingMode) {
      case EditingMode.vertexEdit:
        final activeHandle = widget.controller.activeHandle.value;
        if (activeHandle != null && activeHandle.type == HandleType.vertex) {
          final vertexIndex = activeHandle.index ?? 0;
          return 'Vertex ${vertexIndex + 1}';
        }
        return 'Selected Vertex';
      case EditingMode.edgeEdit:
        final activeHandle = widget.controller.activeHandle.value;
        if (activeHandle != null &&
            activeHandle.type == HandleType.cubicBezierControl) {
          final edgeIndex = activeHandle.index ?? 0;
          final controlIndex = activeHandle.subIndex ?? 0;
          return 'Edge ${edgeIndex + 1} Control ${controlIndex + 1}';
        }
        return 'Selected Control';
      case EditingMode.defaultSelection:
        return widget.shapeData?.type
                .toString()
                .split('.')
                .last
                .capitalizeFirst ??
            'Shape';
    }
  }

  /// Check if dimension controls should be enabled
  bool get _areDimensionControlsEnabled =>
      _currentEditingMode == EditingMode.defaultSelection;

  /// Check if rotation controls should be enabled
  bool get _areRotationControlsEnabled =>
      _currentEditingMode == EditingMode.defaultSelection;

  /// Check if unit toggle should be enabled
  bool get _isUnitToggleEnabled =>
      _currentEditingMode == EditingMode.defaultSelection;

  /// Get the currently selected vertex index for the active editing mode
  int get _activeVertexIndex {
    if (_currentEditingMode == EditingMode.vertexEdit) {
      final activeHandle = widget.controller.activeHandle.value;
      if (activeHandle != null && activeHandle.type == HandleType.vertex) {
        return activeHandle.index ?? -1;
      }
    }
    return -1;
  }

  /// Get the currently selected edge control info for edge editing mode
  ({int edgeIndex, int controlIndex})? get _activeEdgeControl {
    if (_currentEditingMode == EditingMode.edgeEdit) {
      final activeHandle = widget.controller.activeHandle.value;
      if (activeHandle != null &&
          activeHandle.type == HandleType.cubicBezierControl) {
        return (
          edgeIndex: activeHandle.index ?? -1,
          controlIndex: activeHandle.subIndex ?? -1
        );
      }
    }
    return null;
  }

  @override
  void initState() {
    super.initState();

    // Ensure gauge values are available (might need better handling if controller loads async)
    assert(widget.controller.stitchesPerCm.value > 0);
    assert(widget.controller.rowsPerCm.value > 0);

    // Check if this is a group shape
    _isGroupShape = widget.shapeData != null &&
        (widget.shapeData is GroupShapeData ||
            widget.shapeData!.type == ShapeType.group);

    // Create focus nodes first (before initializing controllers)
    _widthFocus = FocusNode();
    _heightFocus = FocusNode();
    _needleFocus = FocusNode(); // Initialize needle focus node
    _rowFocus = FocusNode(); // Initialize row focus node
    _rotationFocus = FocusNode(); // Initialize rotation focus node

    // Setup focus listeners to track editing state
    _widthFocus.addListener(_onFocusChange);
    _heightFocus.addListener(_onFocusChange);
    _needleFocus.addListener(_onFocusChange); // Add listener for needle focus
    _rowFocus.addListener(_onFocusChange); // Add listener for row focus
    _rotationFocus
        .addListener(_onFocusChange); // Add listener for rotation focus

    // Initialize controllers with current values
    // Create empty controllers first to avoid null issues
    _widthController = TextEditingController();
    _heightController = TextEditingController();
    _needleController = TextEditingController(); // Initialize needle controller
    _rowController = TextEditingController(); // Initialize row controller
    _rotationController =
        TextEditingController(); // Initialize rotation controller
    _initializeControllers();
    _updateNeedlePositionDisplay(); // Initialize needle display
    _updateRowPositionDisplay(); // Initialize row display

    // Important: Set the handle interaction flag to true when the HUD is shown
    // This prevents the shape from being deselected when interacting with the HUD
    widget.controller.isHandleInteraction = true;
  }

  void _initializeControllers() {
    // Store current focus state and selection
    final wasWidthFocused = _widthFocus.hasFocus;
    final wasHeightFocused = _heightFocus.hasFocus;
    final wasRotationFocused = _rotationFocus.hasFocus;
    final wasNeedleFocused = _needleFocus.hasFocus;
    final wasRowFocused = _rowFocus.hasFocus;

    // Store current selection
    TextSelection? widthSelection;
    TextSelection? heightSelection;
    TextSelection? rotationSelection;
    TextSelection? needleSelection;
    TextSelection? rowSelection;

    // Check if controllers are initialized before accessing selection
    try {
      widthSelection = _widthController.selection;
      heightSelection = _heightController.selection;
      needleSelection = _needleController.selection;
      rowSelection = _rowController.selection;
      // Don't restore rotation selection if focused, as it can be jarring
      if (!wasRotationFocused) {
        rotationSelection = _rotationController.selection;
      }
    } catch (e) {
      // Controllers might not be initialized yet during first build
    }

    // Get the latest shape data from controller if possible
    final shapeData = widget.shapeKey != null
        ? widget.controller.getShapeState(widget.shapeKey)
        : widget.shapeData;

    // --- Use GeometryUtils and DimensionUtils for consistent calculation ---
    if (shapeData == null) {
      // Handle the case when shapeData is null
      _widthController.text = "0";
      _heightController.text = "0";
      _rotationController.text = "0";
      return;
    }

    final dynamic boundsResult =
        GeometryUtils.calculateAccurateBoundingRect(shapeData);
    Rect rect;
    if (boundsResult is GroupBoundsData) {
      rect = boundsResult.bounds;
    } else if (boundsResult is Rect) {
      rect = boundsResult;
    } else {
      rect = shapeData.boundingRect; // Fallback
    }

    final widthInPixels = rect.width;
    final heightInPixels = rect.height;

    // Get controller properties needed for DimensionUtils
    final cellWidth = widget.controller.gridSystem.cellWidth;
    final aspectRatio = widget.controller.aspectRatio.value;
    final spcm = widget.controller.stitchesPerCm.value;
    final rpcm = widget.controller.rowsPerCm.value;

    // Check if gauge values are valid before using them
    if (spcm <= 0 || rpcm <= 0 || cellWidth <= 0 || aspectRatio <= 0) {
      debugPrint(
          "Warning: Invalid gauge or grid values in ShapePropertyHUD._initializeControllers");
      // Set default values or show error? For now, set to 0.
      _widthController.text = "0";
      _heightController.text = "0";
    } else {
      if (widget.controller.showMetricValues.value) {
        // Convert pixels to cm using DimensionUtils
        final widthInCm =
            DimensionUtils.pixelsToCmWidth(widthInPixels, cellWidth, spcm);
        final heightInCm = DimensionUtils.pixelsToCmHeight(
            heightInPixels, cellWidth, aspectRatio, rpcm);

        // Use standardized formatting for display with locale-aware decimal separator
        _widthController.text = DimensionUtils.formatCmForDisplay(widthInCm);
        _heightController.text = DimensionUtils.formatCmForDisplay(heightInCm);
      } else {
        // Convert pixels to stitches/rows using DimensionUtils
        final stitches =
            DimensionUtils.pixelsToStitches(widthInPixels, cellWidth);
        final rows =
            DimensionUtils.pixelsToRows(heightInPixels, cellWidth, aspectRatio);

        // Use standardized formatting for display
        _widthController.text =
            DimensionUtils.formatGridUnitsForDisplay(stitches);
        _heightController.text = DimensionUtils.formatGridUnitsForDisplay(rows);
      }
    }
    // --- End Dimension Calculation Refactor ---

    // Initialize rotation controller with shape rotation value in degrees
    final rotationDegrees =
        shapeData.visualRotation; // Reads visualRotation (degrees)
    final roundedRotation = rotationDegrees.round().toString();
    // Update rotation text ONLY if not focused OR text differs
    if (!wasRotationFocused || _rotationController.text != roundedRotation) {
      _rotationController.text = roundedRotation;
    }

    // Restore selection if it was active and field not focused
    if (wasWidthFocused && widthSelection != null) {
      _widthController.selection = widthSelection;
    }

    if (wasHeightFocused && heightSelection != null) {
      _heightController.selection = heightSelection;
    }

    if (!wasRotationFocused && rotationSelection != null) {
      _rotationController.selection = rotationSelection;
    }

    if (wasNeedleFocused && needleSelection != null) {
      _needleController.selection = needleSelection;
    }

    if (wasRowFocused && rowSelection != null) {
      _rowController.selection = rowSelection;
    }

    // Schedule focus restoration after the build cycle
    if (wasWidthFocused ||
        wasHeightFocused ||
        wasRotationFocused ||
        wasNeedleFocused ||
        wasRowFocused) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          if (wasWidthFocused) {
            FocusScope.of(context).requestFocus(_widthFocus);
          } else if (wasHeightFocused) {
            FocusScope.of(context).requestFocus(_heightFocus);
          } else if (wasRotationFocused) {
            FocusScope.of(context).requestFocus(_rotationFocus);
          } else if (wasNeedleFocused) {
            FocusScope.of(context).requestFocus(_needleFocus);
          } else if (wasRowFocused) {
            FocusScope.of(context).requestFocus(_rowFocus);
          }
        }
      });
    }

    // Update needle position display
    _updateNeedlePositionDisplay();

    // Update row position display
    _updateRowPositionDisplay();
  }

  void _onFocusChange() {
    final isAnyFocused = _widthFocus.hasFocus ||
        _heightFocus.hasFocus ||
        _needleFocus.hasFocus ||
        _rowFocus.hasFocus ||
        _rotationFocus.hasFocus;

    // Track when a field gets focus
    if (_widthFocus.hasFocus && !_isEditingWidth) {
      // Check _isEditingWidth flag
      _originalWidthValue = _widthController.text; // Store original value
      // Clear dimension error when focusing on either width or height
      if (_showError) {
        setState(() {
          _errorMessage = null;
          _showError = false;
        });
      }
    }
    if (_heightFocus.hasFocus && !_isEditingHeight) {
      // Check _isEditingHeight flag
      _originalHeightValue = _heightController.text; // Store original value
      // Clear dimension error when focusing on either width or height
      if (_showError) {
        setState(() {
          _errorMessage = null;
          _showError = false;
        });
      }
    }
    if (_needleFocus.hasFocus && !_isEditingNeedle) {
      // Check _isEditingNeedle flag
      _originalNeedleValue = _needleController.text; // Store original value
      // Clear error when focusing
      if (_needlePositionError != null) {
        setState(() {
          _needlePositionError = null;
        });
      }
    }
    if (_rowFocus.hasFocus && !_isEditingRow) {
      // Check _isEditingRow flag
      _originalRowValue = _rowController.text; // Store original value
      // Clear error when focusing
      if (_rowPositionError != null) {
        setState(() {
          _rowPositionError = null;
        });
      }
    }
    if (_rotationFocus.hasFocus && !_isEditingRotation) {
      // Check _isEditingRotation flag
      _originalRotationValue =
          _rotationController.text; // Store original value when focus gained
      // Clear error when focusing
      if (_rotationError != null) {
        setState(() {
          _rotationError = null;
        });
      }
    }

    // When focus is lost, ensure history is tracked for changes
    if (!isAnyFocused && _isEditing) {
      // Check width/height changes ON FOCUS LOSS
      if (_isEditingWidth) {
        _updateShapeDimensions(
            trackHistory: (_originalWidthValue != _widthController.text));
      }
      if (_isEditingHeight) {
        // Call update only once if both were edited, _updateShapeDimensions handles both
        if (!_isEditingWidth) {
          // Avoid double call if width also lost focus
          _updateShapeDimensions(
              trackHistory: (_originalHeightValue != _heightController.text));
        }
      }

      // Check needle position changes ON FOCUS LOSS
      if (_isEditingNeedle) {
        // Check the flag
        // Handle final completion: validate format, apply WITH history tracking if value changed
        _handleNeedleInputCompletion(
            trackHistory: (_originalNeedleValue != _needleController.text));
      }

      // Check row position changes ON FOCUS LOSS
      if (_isEditingRow) {
        // Handle final completion: validate format, apply WITH history tracking if value changed
        _handleRowInputCompletion(
            trackHistory: (_originalRowValue != _rowController.text));
      }

      // Check rotation changes ON FOCUS LOSS
      if (_isEditingRotation) {
        // Check the flag
        // Handle final completion: validate, apply WITH history tracking if value changed
        _handleRotationInputCompletion(
            trackHistory: (_originalRotationValue != _rotationController.text));
      }

      // Reset editing flags
      _isEditingWidth = false;
      _isEditingHeight = false;
      _isEditingNeedle = false;
      _isEditingRow = false;
      _isEditingRotation = false;
    }

    // Reset _isEditing flag only if no field has focus anymore
    // Must happen *after* the focus loss check above
    if (!isAnyFocused) {
      _isEditing = false;
    } else {
      _isEditing = true; // Set isEditing if any field gets focus
    }

    // When focusing on text fields, set controller flag to prevent deselection
    if (isAnyFocused) {
      widget.controller.isHandleInteraction = true;
    } else {
      // Reset flag after a short delay when focus is lost
      Future.delayed(const Duration(milliseconds: 100), () {
        if (mounted) {
          widget.controller.isHandleInteraction = false;
        }
      });
    }
  }

  void _hideHUD() {
    // Reset the interaction flag immediately
    widget.controller.isHandleInteraction = false;
    // Deselect the shape(s) which will remove the HUD
    widget.controller.deselectAllShapes();
  }

  void _updateShapeDimensions(
      {bool trackHistory = true, bool preserveFocus = false}) {
    try {
      double? widthValue;
      double? heightValue;
      String currentWidthText = _widthController.text;
      String currentHeightText = _heightController.text;

      // Use the new decimal input helper for parsing
      final widthValidation = DimensionUtils.validateUserDecimalInput(
          currentWidthText,
          requirePositive: true);
      final heightValidation = DimensionUtils.validateUserDecimalInput(
          currentHeightText,
          requirePositive: true);

      // Check if parsing was successful
      if (!widthValidation.isValid || !heightValidation.isValid) {
        String errorMessage;
        if (!widthValidation.isValid && !heightValidation.isValid) {
          // Both invalid - show general message
          errorMessage = 'shapeEditor_propertyHud_errors_invalidDecimal'.tr;
        } else if (!widthValidation.isValid) {
          errorMessage = 'shapeEditor_propertyHud_errors_invalidWidth'.tr;
        } else {
          errorMessage = 'shapeEditor_propertyHud_errors_invalidHeight'.tr;
        }

        setState(() {
          _errorMessage = errorMessage;
          _showError = true;
        });
        if (trackHistory) {
          widget.controller
              .finishHistoryTracking(); // Ensure history ends if started
        }
        return;
      }

      widthValue = widthValidation.value!;
      heightValue = heightValidation.value!;

      // Clear parsing error if it was previously shown
      if (_showError &&
          _errorMessage != null &&
          (_errorMessage!
                  .contains('shapeEditor_propertyHud_errors_invalidDecimal') ||
              _errorMessage!
                  .contains('shapeEditor_propertyHud_errors_invalidWidth') ||
              _errorMessage!
                  .contains('shapeEditor_propertyHud_errors_invalidHeight') ||
              _errorMessage!
                  .contains('shapeEditor_propertyHud_errors_positive'))) {
        setState(() {
          _errorMessage = null;
          _showError = false;
        });
      }

      // Get current shape data
      if (widget.shapeKey == null) {
        if (trackHistory) widget.controller.finishHistoryTracking();
        return;
      }

      final ShapeData? currentData =
          widget.controller.getShapeState(widget.shapeKey);
      if (currentData == null) {
        if (trackHistory) widget.controller.finishHistoryTracking();
        return;
      }

      // --- Use DimensionUtils for pixel calculation ---
      final cellWidth = widget.controller.gridSystem.cellWidth;
      final aspectRatio = widget.controller.aspectRatio.value;
      final spcm = widget.controller.stitchesPerCm.value;
      final rpcm = widget.controller.rowsPerCm.value;

      // Get board boundaries
      final boardSize = _getBoardSize();

      double widthInPixels;
      double heightInPixels;

      // Check for invalid gauge/grid values before calculation
      if (spcm <= 0 || rpcm <= 0 || cellWidth <= 0 || aspectRatio <= 0) {
        debugPrint(
            "Error: Invalid gauge or grid values in ShapePropertyHUD._updateShapeDimensions");
        setState(() {
          _errorMessage = 'shapeEditor_propertyHud_errors_gaugeSetup'.tr;
          _showError = true;
        });
        if (trackHistory) widget.controller.finishHistoryTracking();
        return;
      }

      if (widget.controller.showMetricValues.value) {
        // Convert input CM to pixels
        widthInPixels =
            DimensionUtils.cmToPixelsWidth(widthValue, cellWidth, spcm);
        heightInPixels = DimensionUtils.cmToPixelsHeight(
            heightValue, cellWidth, aspectRatio, rpcm);
      } else {
        // Convert input Stitches/Rows (as int) to pixels
        widthInPixels =
            DimensionUtils.stitchesToPixels(widthValue.round(), cellWidth);
        heightInPixels = DimensionUtils.rowsToPixels(
            heightValue.round(), cellWidth, aspectRatio);
      }
      // --- End Dimension Calculation Refactor ---

      // Constrain dimensions check
      final maxWidth = boardSize.width * 0.95;
      final maxHeight = boardSize.height * 0.98;
      double maxAllowedWidth = maxWidth;
      double maxAllowedHeight = maxHeight;

      if (widget.controller.isMirrorModeActive.value) {
        maxAllowedWidth = (boardSize.width / 2);
      }

      bool widthExceedsConstraints = widthInPixels > maxAllowedWidth;
      bool heightExceedsConstraints = heightInPixels > maxAllowedHeight;
      String? constraintErrorMsg = null;

      if (widthExceedsConstraints || heightExceedsConstraints) {
        // Convert max allowed pixel values back to user-friendly units using DimensionUtils
        double maxAllowedWidthValue, maxAllowedHeightValue;
        String widthUnit, heightUnit;

        if (widget.controller.showMetricValues.value) {
          maxAllowedWidthValue =
              DimensionUtils.pixelsToCmWidth(maxAllowedWidth, cellWidth, spcm);
          maxAllowedHeightValue = DimensionUtils.pixelsToCmHeight(
              maxAllowedHeight, cellWidth, aspectRatio, rpcm);
          widthUnit = 'shapeEditor_propertyHud_units_cm'.tr;
          heightUnit = 'shapeEditor_propertyHud_units_cm'.tr;
        } else {
          maxAllowedWidthValue =
              DimensionUtils.pixelsToStitches(maxAllowedWidth, cellWidth)
                  .toDouble();
          maxAllowedHeightValue = DimensionUtils.pixelsToRows(
                  maxAllowedHeight, cellWidth, aspectRatio)
              .toDouble();
          widthUnit = 'shapeEditor_propertyHud_units_st'.tr;
          heightUnit = 'shapeEditor_propertyHud_units_rw'.tr;
        }

        if (widthExceedsConstraints && heightExceedsConstraints) {
          constraintErrorMsg =
              'shapeEditor_propertyHud_errors_maxSize'.trParams({
            'width': DimensionUtils.formatCmForDisplay(maxAllowedWidthValue),
            'unit': widthUnit,
            'height': DimensionUtils.formatCmForDisplay(maxAllowedHeightValue),
            'unitHeight': heightUnit, // Ensure separate unit param if needed
          });
        } else if (widthExceedsConstraints) {
          constraintErrorMsg =
              'shapeEditor_propertyHud_errors_maxWidth'.trParams({
            'width': DimensionUtils.formatCmForDisplay(maxAllowedWidthValue),
            'unit': widthUnit
          });
        } else {
          // heightExceedsConstraints
          constraintErrorMsg =
              'shapeEditor_propertyHud_errors_maxHeight'.trParams({
            'height': DimensionUtils.formatCmForDisplay(maxAllowedHeightValue),
            'unit': heightUnit
          });
        }

        setState(() {
          _errorMessage = constraintErrorMsg;
          _showError = true;
        });

        if (trackHistory) widget.controller.finishHistoryTracking();
        // Revert text fields to max allowed values? No, keep user input and show error.
        return; // Don't proceed with resizing if constraints exceeded
      }

      // --- Constraints check passed ---
      // Clear constraint error message if it was previously shown for constraints
      if (_showError &&
          _errorMessage != null &&
          (_errorMessage!
                  .startsWith('shapeEditor_propertyHud_errors_maxSize') ||
              _errorMessage!
                  .startsWith('shapeEditor_propertyHud_errors_maxWidth') ||
              _errorMessage!
                  .startsWith("shapeEditor_propertyHud_errors_maxHeight"))) {
        setState(() {
          _errorMessage = null;
          _showError = false;
        });
      }

      // If we get here, the dimensions are valid and within constraints, apply them
      if (trackHistory) {
        widget.controller.startHistoryTracking(
            _isGroupShape ? "Resize Group" : "Manual Size Edit");
      }

      // --- Apply scaling logic based on calculated target pixel dimensions ---
      ShapeData newUnconstrainedShapeData;
      // Use accurate bounds for scaling calculation
      final dynamic currentBoundsResult =
          GeometryUtils.calculateAccurateBoundingRect(currentData);
      Rect currentRect;
      if (currentBoundsResult is GroupBoundsData) {
        currentRect = currentBoundsResult.bounds;
      } else if (currentBoundsResult is Rect) {
        currentRect = currentBoundsResult;
      } else {
        currentRect = currentData.boundingRect; // Fallback
      }

      if (_isGroupShape && currentData is GroupShapeData) {
        final groupData = currentData;
        final currentWidth = currentRect.width; // Use accurate width
        final currentHeight = currentRect.height; // Use accurate height
        final scaleX = (currentWidth > 0) ? widthInPixels / currentWidth : 1.0;
        final scaleY =
            (currentHeight > 0) ? heightInPixels / currentHeight : 1.0;

        final center = groupData.center; // Use group's center for scaling
        final newVertices = groupData.vertices.map((v) {
          final dx = v.dx - center.dx;
          final dy = v.dy - center.dy;
          return Offset(center.dx + dx * scaleX, center.dy + dy * scaleY);
        }).toList();

        // Recalculate bounding rect after scaling
        final newRect = Rect.fromCenter(
            center: center, width: widthInPixels, height: heightInPixels);

        final updatedChildShapes =
            groupData.transformChildShapes(scaleX: scaleX, scaleY: scaleY);

        // Ensure the copyWith includes center and boundingRect updates
        final newGroupData = groupData.copyWith(
            vertices: newVertices,
            center: center, // Keep center the same
            boundingRect: newRect) as GroupShapeData;
        newUnconstrainedShapeData =
            newGroupData.copyWithChildren(childShapes: updatedChildShapes);
      } else {
        // Regular shape
        final currentWidth = currentRect.width; // Use accurate width
        final currentHeight = currentRect.height; // Use accurate height

        // Handle potential division by zero if current dimensions are zero
        final scaleX = (currentWidth > 0) ? widthInPixels / currentWidth : 1.0;
        final scaleY =
            (currentHeight > 0) ? heightInPixels / currentHeight : 1.0;

        // Scale vertices around center
        final center = currentData.center;
        final newVertices = currentData.vertices.map((v) {
          final dx = v.dx - center.dx;
          final dy = v.dy - center.dy;
          return Offset(
            center.dx + dx * scaleX,
            center.dy + dy * scaleY,
          );
        }).toList();

        // Recalculate bounding rect from new vertices or center/size
        final newRect = Rect.fromCenter(
            center: center, width: widthInPixels, height: heightInPixels);

        // Create new shape data with updated vertices, center, and bounding rect
        newUnconstrainedShapeData = currentData.copyWith(
          vertices: newVertices,
          center: center, // Keep the center
          boundingRect: newRect,
        );
      }

      // Ensure shape stays within boundaries
      final constrainedShapeData =
          _constrainShapeWithinBoundaries(newUnconstrainedShapeData, boardSize);

      // Update shape state FIRST (this applies mirror constraints if needed)
      // Pass the boundary-constrained data to saveShapeState
      widget.controller.saveShapeState(widget.shapeKey!, constrainedShapeData);

      // Get the potentially further constrained state AFTER saving (includes mirror constraints)
      final finalConstrainedData =
          widget.controller.getShapeState(widget.shapeKey);
      if (finalConstrainedData == null) {
        debugPrint(
            "Error: Shape state became null after saving in HUD update.");
        setState(() {
          _errorMessage = 'shapeEditor_propertyHud_errors_shapeStateNull'.tr;
          _showError = true;
        });
        if (trackHistory) {
          widget.controller
              .finishHistoryTracking(); // Finish history if started
        }
        return; // Safety check
      }

      // Force rebuild the shape in the UI using the FINAL constrained data
      _forceRebuildShape(widget.shapeKey!, finalConstrainedData);

      // Check if the FINAL dimensions differ from the REQUESTED dimensions
      // Use accurate bounds for final check
      final dynamic finalBoundsResult =
          GeometryUtils.calculateAccurateBoundingRect(finalConstrainedData);
      Rect finalRect;
      if (finalBoundsResult is GroupBoundsData) {
        finalRect = finalBoundsResult.bounds;
      } else if (finalBoundsResult is Rect) {
        finalRect = finalBoundsResult;
      } else {
        finalRect = finalConstrainedData.boundingRect; // Fallback
      }

      final finalWidthPixels = finalRect.width;
      final finalHeightPixels = finalRect.height;

      // Use a small tolerance for floating point comparison
      final tolerance =
          0.1; // Slightly increased tolerance for pixel comparisons
      if ((finalWidthPixels - widthInPixels).abs() > tolerance ||
          (finalHeightPixels - heightInPixels).abs() > tolerance) {
        // If the shape was constrained (by boundaries or mirror mode), update the text fields
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            // Get new dimensions in appropriate units based on the FINAL pixel data
            // Use DimensionUtils for conversion
            double newWidthValue, newHeightValue;

            if (widget.controller.showMetricValues.value) {
              newWidthValue = DimensionUtils.pixelsToCmWidth(
                  finalWidthPixels, cellWidth, spcm);
              newHeightValue = DimensionUtils.pixelsToCmHeight(
                  finalWidthPixels, cellWidth, aspectRatio, rpcm);
            } else {
              newWidthValue =
                  DimensionUtils.pixelsToStitches(finalWidthPixels, cellWidth)
                      .toDouble();
              newHeightValue = DimensionUtils.pixelsToRows(
                      finalHeightPixels, cellWidth, aspectRatio)
                  .toDouble();
            }

            // Update text controllers with the final constrained values using locale-aware formatting
            if (!preserveFocus || !_widthFocus.hasFocus) {
              _widthController.text = widget.controller.showMetricValues.value
                  ? DimensionUtils.formatCmForDisplay(newWidthValue)
                  : DimensionUtils.formatGridUnitsForDisplay(
                      newWidthValue.round());
              // Move cursor to end after update
              _widthController.selection = TextSelection.fromPosition(
                TextPosition(offset: _widthController.text.length),
              );
            }

            if (!preserveFocus || !_heightFocus.hasFocus) {
              _heightController.text = widget.controller.showMetricValues.value
                  ? DimensionUtils.formatCmForDisplay(newHeightValue)
                  : DimensionUtils.formatGridUnitsForDisplay(
                      newHeightValue.round());
              // Move cursor to end after update
              _heightController.selection = TextSelection.fromPosition(
                TextPosition(offset: _heightController.text.length),
              );
            }
          }
        });
      }
      // --- End Scaling Logic ---

      // Finish history tracking if it was started
      if (trackHistory) {
        widget.controller.finishHistoryTracking();
      }
    } catch (e, stackTrace) {
      // Added stackTrace
      debugPrint(
          'Error updating shape dimensions: $e\n$stackTrace'); // Log error with stacktrace
      // Ensure history tracking is finished on error
      widget.controller.finishHistoryTracking();
    }
  }

  @override
  void dispose() {
    _debounceTimer?.cancel(); // Cancel timer on dispose
    // Reset the handle interaction flag when the HUD is disposed
    // Use a small delay to prevent immediate deselection
    Future.delayed(Duration.zero, () {
      // Removed mount check here as it's generally safe in dispose
      // Check if controller still exists? (Might be overkill)
      widget.controller.isHandleInteraction = false;
    });

    _widthController.dispose();
    _heightController.dispose();
    _needleController.dispose();
    _rowController.dispose();
    _rotationController.dispose();
    _widthFocus.removeListener(_onFocusChange); // Remove listeners
    _heightFocus.removeListener(_onFocusChange);
    _needleFocus.removeListener(_onFocusChange);
    _rowFocus.removeListener(_onFocusChange);
    _rotationFocus.removeListener(_onFocusChange);
    _widthFocus.dispose();
    _heightFocus.dispose();
    _needleFocus.dispose();
    _rowFocus.dispose();
    _rotationFocus.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(ShapePropertyHUD oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Check if the shape type has changed
    final newIsGroupShape = widget.shapeData != null &&
        (widget.shapeData is GroupShapeData ||
            widget.shapeData!.type == ShapeType.group);
    if (newIsGroupShape != _isGroupShape) {
      _isGroupShape = newIsGroupShape;
      // Reinitialize controllers if shape type changes (e.g., group/ungroup)
      // Update controllers only if not currently editing
      if (!_isEditing) {
        _initializeControllers();
      }
    }

    // Update controllers only if shape data actually changed AND relevant field is not focused
    if (oldWidget.shapeData != widget.shapeData) {
      // --- Use accurate bounds for comparison and calculation ---
      if (oldWidget.shapeData != null && widget.shapeData != null) {
        final dynamic oldBoundsResult =
            GeometryUtils.calculateAccurateBoundingRect(oldWidget.shapeData!);
        final dynamic newBoundsResult =
            GeometryUtils.calculateAccurateBoundingRect(widget.shapeData!);

        // Handle potential null or unexpected types from calculateAccurateBoundingRect
        Rect? oldRect;
        if (oldBoundsResult is Rect) {
          oldRect = oldBoundsResult;
        } else if (oldBoundsResult is GroupBoundsData) {
          oldRect = oldBoundsResult.bounds;
        }

        Rect? newRect;
        if (newBoundsResult is Rect) {
          newRect = newBoundsResult;
        } else if (newBoundsResult is GroupBoundsData) {
          newRect = newBoundsResult.bounds;
        }

        // Proceed only if we have valid bounds for geometry-related updates
        if (oldRect != null && newRect != null) {
          // --- Width/Height Update ---
          final widthChanged = (oldRect.width - newRect.width).abs() > 0.1;
          final heightChanged = (oldRect.height - newRect.height).abs() > 0.1;

          if ((widthChanged || heightChanged) &&
              !_widthFocus.hasFocus &&
              !_heightFocus.hasFocus) {
            // Recalculate display values based on new bounds using DimensionUtils
            final spcm = widget.controller.stitchesPerCm.value;
            final rpcm = widget.controller.rowsPerCm.value;
            final cellWidth = widget.controller.gridSystem.cellWidth;
            final aspectRatio = widget.controller.aspectRatio.value;
            final widthInPixels = newRect.width;
            final heightInPixels = newRect.height;
            double newWidthValue, newHeightValue;
            int formatFixed = widget.controller.showMetricValues.value ? 1 : 0;

            // Check for invalid gauge/grid values before calculation
            if (spcm <= 0 || rpcm <= 0 || cellWidth <= 0 || aspectRatio <= 0) {
              debugPrint(
                  "Warning: Invalid gauge or grid values in ShapePropertyHUD.didUpdateWidget");
              newWidthValue = 0.0;
              newHeightValue = 0.0;
            } else {
              if (widget.controller.showMetricValues.value) {
                // Convert pixels to cm
                newWidthValue = DimensionUtils.pixelsToCmWidth(
                    widthInPixels, cellWidth, spcm);
                newHeightValue = DimensionUtils.pixelsToCmHeight(
                    heightInPixels, cellWidth, aspectRatio, rpcm);
              } else {
                // Convert pixels to stitches/rows
                newWidthValue =
                    DimensionUtils.pixelsToStitches(widthInPixels, cellWidth)
                        .toDouble();
                newHeightValue = DimensionUtils.pixelsToRows(
                        heightInPixels, cellWidth, aspectRatio)
                    .toDouble();
                formatFixed = 0; // Ensure whole numbers for stitches/rows
              }
            }

            // Update controllers ONLY if not focused
            if (widthChanged && !_widthFocus.hasFocus) {
              final newText = newWidthValue.toStringAsFixed(formatFixed);
              if (_widthController.text != newText) {
                _widthController.text = newText;
              }
            }
            if (heightChanged && !_heightFocus.hasFocus) {
              final newText = newHeightValue.toStringAsFixed(formatFixed);
              if (_heightController.text != newText) {
                _heightController.text = newText;
              }
            }
          }

          // --- Position Update (both needle and row) ---
          final positionChanged =
              (oldRect.topLeft - newRect.topLeft).distance > 0.1;
          if (positionChanged) {
            // Update needle position if not focused
            if (!_needleFocus.hasFocus) {
              _updateNeedlePositionDisplay();
            }

            // Update row position if not focused
            if (!_rowFocus.hasFocus) {
              _updateRowPositionDisplay();
            }
          }
        } // End of valid bounds check

        // --- Rotation Update ---
        // Check visualRotation specifically, as bounds might not change for pure rotation
        if (oldWidget.shapeData!.visualRotation !=
                widget.shapeData!.visualRotation &&
            !_rotationFocus.hasFocus) {
          // <--- Check focus here
          final rotationDegrees = widget
              .shapeData!.visualRotation; // Reads visualRotation (degrees)
          final roundedRotation = rotationDegrees.round().toString();
          // Only update if text is different to avoid unnecessary updates/cursor jumps
          if (_rotationController.text != roundedRotation) {
            _rotationController.text = roundedRotation;
            // Avoid manipulating selection if not focused
          }
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isSmallPhoneScreen = MediaQuery.of(context).size.width < 450;
    final bottomPadding =
        MediaQuery.of(context).viewInsets.bottom > 0 ? 4.0 : 8.0;

    // Define text styles for labels and input fields based on the image
    final labelStyle = TextStyle(
        fontSize: 13, // Compact
        color: Get.isDarkMode ? Colors.white70 : Colors.black87);
    final inputTextStyle =
        TextStyle(fontSize: 13, fontWeight: FontWeight.bold); // Compact
    final textFieldDecoration = InputDecoration(
      isDense: true,
      contentPadding:
          const EdgeInsets.symmetric(horizontal: 6, vertical: 8), // Compact
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(4), // Slightly smaller radius
        borderSide: BorderSide(
          color: Colors.grey.shade300, // Lighter border
        ),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(4),
        borderSide: BorderSide(
          color: Theme.of(context).primaryColor,
          width: 1,
        ),
      ),
    );

    // Wrap the entire UI in Obx to make it reactive
    return Obx(() {
      // Get the latest shape data from the controller
      final currentShapeData = widget.shapeKey != null
          ? widget.controller.getShapeState(widget.shapeKey)
          : widget.shapeData;

      // Update controllers if shape data has changed and fields are not focused
      if (currentShapeData != null &&
          !_widthFocus.hasFocus &&
          !_heightFocus.hasFocus &&
          !_needleFocus.hasFocus &&
          !_rowFocus.hasFocus &&
          !_rotationFocus.hasFocus) {
        // Schedule update after current frame to avoid setState during build
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            _initializeControllers();
          }
        });
      }

      return Listener(
        onPointerDown: (_) {
          // Set the flag on any interaction with the HUD
          widget.controller.isHandleInteraction = true;
        },
        onPointerUp: (_) {
          // Schedule flag reset after the tap event is fully processed
          Future.delayed(Duration(milliseconds: 50), () {
            if (mounted) {
              // Only clear the flag if we're not focused on a text field
              if (!_widthFocus.hasFocus &&
                  !_heightFocus.hasFocus &&
                  !_needleFocus.hasFocus &&
                  !_rowFocus.hasFocus &&
                  !_rotationFocus.hasFocus) {
                widget.controller.isHandleInteraction = false;
              }
            }
          });
        },
        child: AbsorbPointer(
          absorbing: false, // Allow child interactions
          child: Material(
            elevation: 8,
            color: Colors.transparent,
            borderRadius: BorderRadius.circular(12), // Main container rounding
            child: Stack(
              // Add Stack to position the close button
              children: [
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
                  decoration: BoxDecoration(
                    color: Get.isDarkMode
                        ? Colors.grey[800]!.withOpacity(0.85)
                        : Colors.grey[200]!.withOpacity(0.95),
                    borderRadius:
                        BorderRadius.circular(12), // Main container rounding
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.15),
                        blurRadius: 6,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      // Error display area
                      if (_showError && _errorMessage != null ||
                          _rotationError != null ||
                          _needlePositionError != null ||
                          _rowPositionError != null)
                        Container(
                          width: double.infinity,
                          padding: const EdgeInsets.only(bottom: 6, top: 2),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              if (_showError && _errorMessage != null)
                                Text(
                                  _errorMessage!,
                                  style: TextStyle(
                                      color: Colors.red.shade600, fontSize: 11),
                                  textAlign: TextAlign.center,
                                ),
                              if (_rotationError != null)
                                Text(
                                  _rotationError!,
                                  style: TextStyle(
                                      color: Colors.red.shade600, fontSize: 11),
                                  textAlign: TextAlign.center,
                                ),
                              if (_needlePositionError != null)
                                Text(
                                  _needlePositionError!,
                                  style: TextStyle(
                                      color: Colors.red.shade600, fontSize: 11),
                                  textAlign: TextAlign.center,
                                ),
                              if (_rowPositionError != null)
                                Text(
                                  _rowPositionError!,
                                  style: TextStyle(
                                      color: Colors.red.shade600, fontSize: 11),
                                  textAlign: TextAlign.center,
                                ),
                            ],
                          ),
                        ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment
                            .start, // Use start alignment to avoid layout issues
                        children: [
                          // Size Section
                          Expanded(
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 6, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.transparent,
                                border: Border.all(
                                  color: Get.isDarkMode
                                      ? Colors.grey.shade600
                                      : Colors.grey.shade400,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('shapeEditor_propertyHud_size'.tr,
                                      style: TextStyle(
                                          fontSize: 14,
                                          fontWeight: FontWeight.w500)),
                                  SizedBox(height: 4), // Consistent spacing
                                  // Width Input
                                  SizedBox(
                                    height: 28, // Consistent row height
                                    child: Row(
                                      children: [
                                        Text(
                                            "shapeEditor_propertyHud_widthShort"
                                                .tr,
                                            style: labelStyle),
                                        SizedBox(width: 6),
                                        Expanded(
                                          child: SizedBox(
                                            height:
                                                28, // Explicit height for TextField container
                                            child: TextField(
                                              controller: _widthController,
                                              focusNode: _widthFocus,
                                              enabled:
                                                  _areDimensionControlsEnabled,
                                              keyboardType: TextInputType
                                                  .numberWithOptions(
                                                      decimal: true),
                                              textAlign: TextAlign.center,
                                              style: inputTextStyle.copyWith(
                                                color:
                                                    !_areDimensionControlsEnabled
                                                        ? (Get.isDarkMode
                                                            ? Colors
                                                                .grey.shade600
                                                            : Colors
                                                                .grey.shade400)
                                                        : null,
                                              ),
                                              decoration:
                                                  textFieldDecoration.copyWith(
                                                fillColor:
                                                    !_areDimensionControlsEnabled
                                                        ? (Get.isDarkMode
                                                            ? Colors
                                                                .grey.shade800
                                                            : Colors
                                                                .grey.shade100)
                                                        : null,
                                                filled:
                                                    !_areDimensionControlsEnabled,
                                              ),
                                              onEditingComplete: () {
                                                if (!_areDimensionControlsEnabled) {
                                                  return;
                                                }
                                                _isEditingWidth = false;
                                                _updateShapeDimensions(
                                                    trackHistory: true,
                                                    preserveFocus: true);
                                                _originalWidthValue =
                                                    _widthController.text;
                                              },
                                              onChanged: (value) {
                                                if (!_areDimensionControlsEnabled) {
                                                  return;
                                                }
                                                if (!_isEditingWidth) {
                                                  _isEditingWidth = true;
                                                  _originalWidthValue ??=
                                                      _widthController.text;
                                                }
                                                Future.delayed(
                                                    const Duration(
                                                        milliseconds: 100), () {
                                                  if (mounted &&
                                                      value ==
                                                          _widthController
                                                              .text) {
                                                    _updateShapeDimensions(
                                                        trackHistory: false,
                                                        preserveFocus: true);
                                                  }
                                                });
                                              },
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  SizedBox(height: 4), // Consistent spacing
                                  // Height Input
                                  SizedBox(
                                    height: 28, // Consistent row height
                                    child: Row(
                                      children: [
                                        Text(
                                            "shapeEditor_propertyHud_heightShort"
                                                .tr,
                                            style: labelStyle),
                                        SizedBox(width: 6),
                                        Expanded(
                                          child: SizedBox(
                                            height:
                                                28, // Explicit height for TextField container
                                            child: TextField(
                                              controller: _heightController,
                                              focusNode: _heightFocus,
                                              enabled:
                                                  _areDimensionControlsEnabled,
                                              keyboardType: TextInputType
                                                  .numberWithOptions(
                                                      decimal: true),
                                              textAlign: TextAlign.center,
                                              style: inputTextStyle.copyWith(
                                                color:
                                                    !_areDimensionControlsEnabled
                                                        ? (Get.isDarkMode
                                                            ? Colors
                                                                .grey.shade600
                                                            : Colors
                                                                .grey.shade400)
                                                        : null,
                                              ),
                                              decoration:
                                                  textFieldDecoration.copyWith(
                                                fillColor:
                                                    !_areDimensionControlsEnabled
                                                        ? (Get.isDarkMode
                                                            ? Colors
                                                                .grey.shade800
                                                            : Colors
                                                                .grey.shade100)
                                                        : null,
                                                filled:
                                                    !_areDimensionControlsEnabled,
                                              ),
                                              onEditingComplete: () {
                                                if (!_areDimensionControlsEnabled)
                                                  return;
                                                _isEditingHeight = false;
                                                _updateShapeDimensions(
                                                    trackHistory: true,
                                                    preserveFocus: true);
                                                _originalHeightValue =
                                                    _heightController.text;
                                              },
                                              onChanged: (value) {
                                                if (!_areDimensionControlsEnabled)
                                                  return;
                                                if (!_isEditingHeight) {
                                                  _isEditingHeight = true;
                                                  _originalHeightValue ??=
                                                      _heightController.text;
                                                }
                                                if (mounted &&
                                                    value ==
                                                        _heightController
                                                            .text) {
                                                  _updateShapeDimensions(
                                                      trackHistory: false,
                                                      preserveFocus: true);
                                                }
                                              },
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  SizedBox(height: 4), // Consistent spacing
                                  // Unit Toggle
                                  SizedBox(
                                    height: 28, // Consistent row height
                                    child: Obx(
                                      () => Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Text(
                                              'shapeEditor_propertyHud_units_cm'
                                                  .tr,
                                              style: labelStyle.copyWith(
                                                  fontSize:
                                                      12, // smaller for toggle
                                                  color: !_isUnitToggleEnabled
                                                      ? (Get.isDarkMode
                                                          ? Colors.grey.shade600
                                                          : Colors
                                                              .grey.shade400)
                                                      : (widget
                                                              .controller
                                                              .showMetricValues
                                                              .value
                                                          ? (Get.isDarkMode
                                                              ? Colors
                                                                  .greenAccent
                                                                  .shade100
                                                              : Colors.green
                                                                  .shade700)
                                                          : Colors
                                                              .grey.shade500))),
                                          SizedBox(width: 2),
                                          Transform.scale(
                                            scale:
                                                0.75, // Adjusted scale for 28px height
                                            child: Switch(
                                              value: !widget
                                                  .controller
                                                  .showMetricValues
                                                  .value, // true if s/r
                                              onChanged: !_isUnitToggleEnabled
                                                  ? null
                                                  : (bool value) {
                                                      widget.controller
                                                          .toggleMetricValues();
                                                      _initializeControllers();
                                                    },
                                              activeColor: Colors.green
                                                  .shade400, // s/r active color
                                              inactiveThumbColor: Colors.green
                                                  .shade400, // cm active color (thumb when inactive)
                                              activeTrackColor: Colors
                                                  .grey.shade400
                                                  .withOpacity(0.5),
                                              inactiveTrackColor: Colors
                                                  .grey.shade400
                                                  .withOpacity(0.5),
                                              thumbColor: WidgetStateProperty
                                                  .resolveWith<Color?>(
                                                      (Set<WidgetState>
                                                          states) {
                                                if (states.contains(
                                                    WidgetState.selected)) {
                                                  return Colors.green
                                                      .shade400; // thumb for s/r (Switch selected means !showMetric)
                                                }
                                                return Colors.green
                                                    .shade400; // thumb for cm (Switch not selected means showMetric)
                                              }),
                                              trackColor: WidgetStateProperty
                                                  .resolveWith<Color?>(
                                                      (Set<WidgetState>
                                                          states) {
                                                return Colors.grey.shade300;
                                              }),
                                              materialTapTargetSize:
                                                  MaterialTapTargetSize
                                                      .shrinkWrap,
                                            ),
                                          ),
                                          SizedBox(width: 2),
                                          Text(
                                              'shapeEditor_propertyHud_units_stRw'
                                                  .tr, // Assuming 's/r'
                                              style: labelStyle.copyWith(
                                                  fontSize:
                                                      12, // smaller for toggle
                                                  color: _isInVertexEditMode
                                                      ? (Get.isDarkMode
                                                          ? Colors.grey.shade600
                                                          : Colors
                                                              .grey.shade400)
                                                      : (!widget
                                                              .controller
                                                              .showMetricValues
                                                              .value
                                                          ? (Get.isDarkMode
                                                              ? Colors
                                                                  .greenAccent
                                                                  .shade100
                                                              : Colors.green
                                                                  .shade700)
                                                          : Colors
                                                              .grey.shade500))),
                                        ],
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(width: 6), // Compact
                          // Position Section
                          Expanded(
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 6, vertical: 4),
                              decoration: BoxDecoration(
                                color: Colors.transparent,
                                border: Border.all(
                                  color: Get.isDarkMode
                                      ? Colors.grey.shade600
                                      : Colors.grey.shade400,
                                  width: 1.0,
                                ),
                                borderRadius: BorderRadius.circular(6),
                              ),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text('shapeEditor_propertyHud_position'.tr,
                                      style: TextStyle(
                                          fontSize: 14, //Compact
                                          fontWeight: FontWeight.w500)),
                                  SizedBox(height: 4), // Consistent spacing

                                  // Needle (Horizontal Position) Controls
                                  SizedBox(
                                    height: 28, // Consistent row height
                                    child: _buildNeedleControls(
                                        inputTextStyle, textFieldDecoration),
                                  ),
                                  SizedBox(height: 4), // Consistent spacing

                                  // Row (Vertical Position) Controls
                                  SizedBox(
                                    height: 28, // Consistent row height
                                    child: _buildRowControls(
                                        inputTextStyle, textFieldDecoration),
                                  ),
                                  SizedBox(height: 4), // Consistent spacing

                                  // Rotation Controls
                                  SizedBox(
                                    height: 28, // Consistent row height
                                    child: _buildRotationControls(
                                        inputTextStyle, textFieldDecoration),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          SizedBox(width: 6), // Compact
                          // Vertically centered snap-to-center button
                          SizedBox(
                            width: 32,
                            // use margin to push button to vertical center
                            child: Column(
                              children: [
                                // Add spacing to push button down to center
                                SizedBox(
                                    height: 50), // Approximate center position
                                // The actual button
                                IconButton(
                                  iconSize: 22,
                                  icon: ShapeTestIcons.snapToCenter(
                                    size: 22,
                                    color: Colors.blue.shade400,
                                    active: true,
                                  ),
                                  onPressed: () {
                                    snapActiveShapeToCenter();
                                  },
                                  tooltip:
                                      'shapeEditor_propertyHud_snapToCenter'.tr,
                                  padding: EdgeInsets.zero,
                                  constraints: BoxConstraints(
                                    minWidth: 32,
                                    minHeight: 32,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                // Close button positioned at top right
                Positioned(
                  top: 2,
                  right: 2,
                  child: SizedBox(
                    width: 28, // Slightly smaller
                    height: 28, // Slightly smaller
                    child: IconButton(
                      icon: const Icon(Icons.close, size: 18),
                      padding: EdgeInsets.zero,
                      tooltip: 'shapeEditor_propertyHud_hidePanel'.tr,
                      onPressed: _hideHUD,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    });
  }

  // Helper method to build Needle (Horizontal Position) controls
  Widget _buildNeedleControls(
      TextStyle inputStyle, InputDecoration decoration) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        GestureDetector(
          onTap: () => _adjustNeedlePosition(-1),
          onLongPress: () => _adjustNeedlePosition(-5),
          child: Tooltip(
            message: 'shapeEditor_propertyHud_decrement'.tr,
            child: InkWell(
              onTap: () => _adjustNeedlePosition(-1),
              child:
                  Icon(Icons.arrow_back_sharp, size: 18), // Standardized size
            ),
          ),
        ),
        SizedBox(width: 4), // Adjusted spacing
        Expanded(
          child: SizedBox(
            height: 28, // Consistent TextField height
            child: TextField(
              controller: _needleController,
              focusNode: _needleFocus,
              textAlign: TextAlign.center,
              keyboardType: TextInputType.text,
              inputFormatters: [], // Keep this for potential future formatters
              style: inputStyle,
              decoration: decoration,
              onChanged: (value) {
                if (!_isEditingNeedle) {
                  _isEditingNeedle = true;
                  _originalNeedleValue ??= _needlePositionString;
                }
                final gridSystem = widget.controller.gridSystem;
                final needleCount = gridSystem.needleCount;
                final targetIndexZeroBased =
                    _parseNeedleString(value.toUpperCase(), needleCount);
                if (targetIndexZeroBased != null) {
                  if (_needlePositionError != null &&
                      (_needlePositionError ==
                              'shapeEditor_propertyHud_errors_invalidNeedleFormat'
                                  .tr ||
                          _needlePositionError ==
                              'shapeEditor_propertyHud_errors_positionGeneric'
                                  .tr)) {
                    setState(() {
                      _needlePositionError = null;
                    });
                  }
                  if (_isInVertexEditMode) {
                    _updateVertexPositionRealtime(targetIndexZeroBased,
                        isHorizontal: true);
                  } else if (_isInEdgeEditMode) {
                    _updateEdgeControlPositionRealtime(targetIndexZeroBased,
                        isHorizontal: true);
                  } else {
                    _updateShapePositionRealtime(targetIndexZeroBased);
                  }
                } else if (value.isNotEmpty) {
                  if (_needlePositionError == null ||
                      _needlePositionError ==
                          'Move constrained by boundaries or mirror mode.') {
                    setState(() {
                      _needlePositionError =
                          'shapeEditor_propertyHud_errors_invalidNeedleFormat'
                              .tr;
                    });
                  }
                }
              },
              onEditingComplete: () {
                _isEditingNeedle = false;
                _handleNeedleInputCompletion(
                    trackHistory:
                        (_originalNeedleValue != _needleController.text));
              },
            ),
          ),
        ),
        SizedBox(width: 4), // Adjusted spacing
        GestureDetector(
          onTap: () => _adjustNeedlePosition(1),
          onLongPress: () => _adjustNeedlePosition(5),
          child: Tooltip(
            message: 'shapeEditor_propertyHud_increment'.tr,
            child: InkWell(
              onTap: () => _adjustNeedlePosition(1),
              child: Icon(Icons.arrow_forward_sharp,
                  size: 18), // Standardized size
            ),
          ),
        ),
        SizedBox(width: 4), // Adjusted spacing
      ],
    );
  }

  // Helper method to build Row (Vertical Position) controls
  Widget _buildRowControls(TextStyle inputStyle, InputDecoration decoration) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        GestureDetector(
          onTap: () => _adjustRowPosition(-1),
          onLongPress: () => _adjustRowPosition(-5),
          child: Tooltip(
            message: 'shapeEditor_propertyHud_decrement'.tr,
            child: InkWell(
              onTap: () => _adjustRowPosition(-1),
              child: Icon(Icons.arrow_upward, size: 18), // Standardized size
            ),
          ),
        ),
        SizedBox(width: 4), // Adjusted spacing
        Expanded(
          child: SizedBox(
            height: 28, // Consistent TextField height
            child: TextField(
              controller: _rowController,
              focusNode: _rowFocus,
              textAlign: TextAlign.center,
              keyboardType: TextInputType.number,
              style: inputStyle,
              decoration: decoration,
              onChanged: (value) {
                if (!_isEditingRow) {
                  _isEditingRow = true;
                  _originalRowValue ??= _rowController.text;
                }
                int? rowNumber;
                try {
                  rowNumber = int.parse(value);
                  if (rowNumber < 1) {
                    throw FormatException('Row must be positive');
                  }
                  if (_rowPositionError ==
                      'shapeEditor_propertyHud_errors_invalidRowFormat'.tr) {
                    setState(() {
                      _rowPositionError = null;
                    });
                  }
                  if (_isInVertexEditMode) {
                    _updateVertexRowPositionRealtime(rowNumber);
                  } else if (_isInEdgeEditMode) {
                    _updateEdgeControlRowPositionRealtime(rowNumber);
                  } else {
                    _updateShapeRowPositionRealtime(rowNumber);
                  }
                } catch (e) {
                  if (_rowPositionError !=
                      'shapeEditor_propertyHud_errors_invalidRowFormat'.tr) {
                    setState(() {
                      _rowPositionError =
                          'shapeEditor_propertyHud_errors_invalidRowFormat'.tr;
                    });
                  }
                }
              },
              onEditingComplete: () {
                _isEditingRow = false;
                _handleRowInputCompletion(
                    trackHistory: (_originalRowValue != _rowController.text));
              },
            ),
          ),
        ),
        SizedBox(width: 4), // Adjusted spacing
        GestureDetector(
          onTap: () => _adjustRowPosition(1),
          onLongPress: () => _adjustRowPosition(5),
          child: Tooltip(
            message: 'shapeEditor_propertyHud_increment'.tr,
            child: InkWell(
              onTap: () => _adjustRowPosition(1),
              child: Icon(Icons.arrow_downward, size: 18), // Standardized size
            ),
          ),
        ),
        SizedBox(width: 4), // Adjusted spacing
      ],
    );
  }

  // Helper method to build Rotation controls
  Widget _buildRotationControls(
      TextStyle inputStyle, InputDecoration decoration) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        GestureDetector(
          onTap:
              !_areRotationControlsEnabled ? null : () => _adjustRotation(-1),
          onLongPress:
              !_areRotationControlsEnabled ? null : () => _adjustRotation(-5),
          child: Tooltip(
            message: 'shapeEditor_propertyHud_rotateCounterClockwise'.tr,
            child: InkWell(
              onTap: _isInEdgeEditMode ? null : () => _adjustRotation(-1),
              child: Icon(FontAwesomeIcons.rotateLeft,
                  size: 18,
                  color: !_areRotationControlsEnabled
                      ? (Get.isDarkMode
                          ? Colors.grey.shade600
                          : Colors.grey.shade400)
                      : null), // Standardized size
            ),
          ),
        ),
        SizedBox(width: 4), // Adjusted spacing
        Expanded(
          child: SizedBox(
            height: 28, // Consistent TextField height
            child: TextField(
              controller: _rotationController,
              focusNode: _rotationFocus,
              enabled: _areRotationControlsEnabled,
              textAlign: TextAlign.center,
              keyboardType: TextInputType.number,
              style: inputStyle.copyWith(
                color: !_areRotationControlsEnabled
                    ? (Get.isDarkMode
                        ? Colors.grey.shade600
                        : Colors.grey.shade400)
                    : null,
              ),
              decoration: decoration.copyWith(
                suffix: Text('°', style: TextStyle(fontSize: 12)),
                fillColor: !_areRotationControlsEnabled
                    ? (Get.isDarkMode
                        ? Colors.grey.shade800
                        : Colors.grey.shade100)
                    : null,
                filled: !_areRotationControlsEnabled,
              ),
              onChanged: (value) {
                if (!_areRotationControlsEnabled) return;
                if (!_isEditingRotation) {
                  _isEditingRotation = true;
                  _originalRotationValue ??= _rotationController.text;
                }

                // Use DecimalInputHelper to parse rotation (can be negative)
                final rotationValidation =
                    DecimalInputHelper.validateDecimalInput(value,
                        requirePositive: false);

                if (rotationValidation.isValid &&
                    rotationValidation.value != null) {
                  // Clear rotation error if parsing was successful
                  if (_rotationError ==
                      'shapeEditor_propertyHud_errors_invalidRotation'.tr) {
                    setState(() {
                      _rotationError = null;
                    });
                  }
                  // Convert to integer for rotation update
                  final rotationDegrees = rotationValidation.value!.round();
                  _updateShapeRotationRealtime(rotationDegrees);
                } else {
                  // Show error only if input is not empty (allow partial typing)
                  if (value.isNotEmpty &&
                      _rotationError !=
                          'shapeEditor_propertyHud_errors_invalidRotation'.tr) {
                    setState(() {
                      _rotationError =
                          'shapeEditor_propertyHud_errors_invalidRotation'.tr;
                    });
                  }
                }
              },
              onEditingComplete: () {
                if (!_areRotationControlsEnabled) return;
                _isEditingRotation = false;
                _handleRotationInputCompletion(
                    trackHistory:
                        (_originalRotationValue != _rotationController.text));
              },
            ),
          ),
        ),
        SizedBox(width: 4), // Adjusted spacing
        GestureDetector(
          onTap: _isInEdgeEditMode ? null : () => _adjustRotation(1),
          onLongPress: _isInEdgeEditMode ? null : () => _adjustRotation(5),
          child: Tooltip(
            message: 'shapeEditor_propertyHud_rotateClockwise'.tr,
            child: InkWell(
              onTap: _isInEdgeEditMode ? null : () => _adjustRotation(1),
              child: Icon(FontAwesomeIcons.rotateRight,
                  size: 18,
                  color: _isInEdgeEditMode
                      ? (Get.isDarkMode
                          ? Colors.grey.shade600
                          : Colors.grey.shade400)
                      : null), // Standardized size
            ),
          ),
        ),
        SizedBox(width: 4), // Adjusted spacing
      ],
    );
  }

  // Helper method to force a rebuild of the shape in the UI
  void _forceRebuildShape(Key shapeKey, ShapeData shapeData) {
    // Find the index only if the widget is mounted
    if (!mounted) return;

    int shapeIndex = -1;
    final currentShapes = widget.controller.shapes; // Use local copy for safety

    // Check length before accessing
    if (currentShapes.isEmpty) return;

    for (int i = 0; i < currentShapes.length; i++) {
      // Check index boundary again inside the loop
      if (i >= currentShapes.length) break;
      if (currentShapes[i].key == shapeKey) {
        shapeIndex = i;
        break;
      }
    }

    // If we found the shape, rebuild it
    if (shapeIndex >= 0) {
      // Store focus state before rebuilding
      final wasWidthFocused = _widthFocus.hasFocus;
      final wasHeightFocused = _heightFocus.hasFocus;

      WidgetsBinding.instance.addPostFrameCallback((_) {
        // Re-check mount status and index validity within the callback
        if (!mounted ||
            shapeIndex >= currentShapes.length ||
            currentShapes[shapeIndex].key != shapeKey) return;

        // Get the current shape to extract properties, check index validity again
        final currentShape = currentShapes[shapeIndex];

        // Determine selection state based on controller's selectedIndices
        final isSelected =
            widget.controller.selectedIndices.contains(shapeIndex);

        // Determine curve mode state from the controller's state map
        final isCurveMode =
            widget.controller.curveModeStates[shapeKey] ?? false;

        // Create a new TransformableShape instance with the updated data
        final newShape = TransformableShape(
          key: shapeKey,
          constraints: currentShape.constraints,
          initialShapeType: currentShape.initialShapeType,
          initialShapeData: shapeData, // Use the passed, final constrained data
          selected: isSelected, // Use current selection state from controller
          initialCurveMode:
              isCurveMode, // Use current curve mode from controller
        );

        // Replace the shape in the controller's list (use update method if available, otherwise direct assignment)
        // Ensure the list hasn't changed size unexpectedly
        if (mounted &&
            shapeIndex < widget.controller.shapes.length &&
            widget.controller.shapes[shapeIndex].key == shapeKey) {
          widget.controller.shapes[shapeIndex] = newShape;
          widget.controller.update(); // Explicitly call update if needed

          // Restore focus after the update
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              if (wasWidthFocused && !_widthFocus.hasFocus) {
                FocusScope.of(context).requestFocus(_widthFocus);
              } else if (wasHeightFocused && !_heightFocus.hasFocus) {
                FocusScope.of(context).requestFocus(_heightFocus);
              }
            }
          });
        }
      });
    } else {
      debugPrint("Shape with key $shapeKey not found for rebuild in HUD.");
    }
  }

  // Helper method to constrain a shape within the drawing board boundaries
  ShapeData _constrainShapeWithinBoundaries(
      ShapeData shapeData, Size boardSize) {
    // Allow a small margin to prevent shapes from being exactly at the edge
    final margin = 10.0;
    final safeWidth = boardSize.width - margin * 2;
    final safeHeight = boardSize.height - margin * 2;

    // Get the shape's bounding rectangle
    final rect = shapeData.boundingRect;
    final center = shapeData.center;

    // Calculate constraints based on the shape's size and board boundaries
    double minX = margin + rect.width / 2;
    double maxX = safeWidth - rect.width / 2 + margin;
    double minY = margin + rect.height / 2;
    double maxY = safeHeight - rect.height / 2 + margin;

    // Constrain the center point
    final newCenterX = center.dx.clamp(minX, maxX);
    final newCenterY = center.dy.clamp(minY, maxY);
    final newCenter = Offset(newCenterX, newCenterY);

    // If the center hasn't changed, return the original shape data
    if (newCenter == center) {
      return shapeData;
    }

    // Calculate the translation delta
    final delta = newCenter - center;

    // If this is a group shape, handle it differently
    if (shapeData is GroupShapeData) {
      final groupData = shapeData;

      // Move all vertices
      final newVertices = groupData.vertices.map((v) => v + delta).toList();

      // Transform all child shapes
      final updatedChildShapes = groupData.transformChildShapes(
        translation: delta,
      );

      // Create new group data
      final newGroupData = groupData.copyWith(
        vertices: newVertices,
        center: newCenter,
        boundingRect: groupData.boundingRect.shift(delta),
      ) as GroupShapeData;

      // Return with children
      return newGroupData.copyWithChildren(
        childShapes: updatedChildShapes,
      );
    } else {
      // For regular shapes
      // Move all vertices
      final newVertices = shapeData.vertices.map((v) => v + delta).toList();

      // Return new shape data
      return shapeData.copyWith(
        vertices: newVertices,
        center: newCenter,
        boundingRect: shapeData.boundingRect.shift(delta),
      );
    }
  }

  // Helper method to get the drawing board size
  Size _getBoardSize({bool extended = false}) {
    // Use the screen size as the board size with zoom adjustment
    final screenSize = widget.screenSize;
    final zoomScale = widget.controller.zoomScale.value;

    // Sanitize screen size to prevent infinity values
    final safeWidth =
        screenSize?.width.isFinite ?? false ? screenSize!.width : Get.width;
    final safeHeight = screenSize?.height.isFinite ?? false
        ? screenSize!.height
        : extended
            ? GridConstants.getExtendedHeight(Get.height)
            : Get.height;

    // The visual area is increased when zoomed out
    // and decreased when zoomed in
    return Size(
      safeWidth / zoomScale,
      safeHeight / zoomScale,
    );
  }

  // --- Updated Method to Update Needle Position Display ---
  void _updateNeedlePositionDisplay({bool forceUpdateText = false}) {
    try {
      // Get the latest shape data from controller if possible
      final shapeData = widget.shapeKey != null
          ? widget.controller.getShapeState(widget.shapeKey)
          : widget.shapeData;

      if (shapeData == null) {
        _needlePositionString = "N/A";
        if (mounted) setState(() {});
        return;
      }

      // Check if we're in vertex edit mode
      if (_isInVertexEditMode) {
        _updateVertexNeedlePositionDisplay(shapeData,
            forceUpdateText: forceUpdateText);
        return;
      }

      // Check if we're in edge edit mode
      if (_isInEdgeEditMode) {
        _updateEdgeControlNeedlePositionDisplay(shapeData,
            forceUpdateText: forceUpdateText);
        return;
      }

      // Default mode: show whole shape position
      // 1. Get current shape data
      final currentData = shapeData;

      // 2. Calculate accurate bounding box
      final dynamic boundsResult =
          GeometryUtils.calculateAccurateBoundingRect(currentData);
      Rect rect;
      if (boundsResult is GroupBoundsData) {
        rect = boundsResult.bounds;
      } else if (boundsResult is Rect) {
        rect = boundsResult;
      } else {
        _needlePositionString = "N/A"; // Error case
        if (mounted) setState(() {});
        return;
      }

      // 3. Get top-left corner
      final topLeftOffset = rect.topLeft;

      // 4. Convert to needle number using GridSystem properties
      final gridSystem = widget.controller.gridSystem;
      // Use Get.width if screen size isn't ready, though it should be.
      final screenWidth = widget.screenSize?.width.isFinite ?? false
          ? widget.screenSize!.width
          : Get.width;

      // Calculate grid properties directly
      final cellWidth = gridSystem.cellWidth;
      final needleCount = gridSystem.needleCount;
      final totalWidth = needleCount * cellWidth;
      final leftEdge = screenWidth / 2 - (totalWidth / 2);

      // Ensure cellWidth is positive to avoid division by zero
      if (cellWidth <= 0) {
        _needlePositionString = "N/A";
        if (mounted) setState(() {});
        return;
      }

      // Calculate the 0-based needle index (closest needle to the left edge)
      final needleIndexZeroBased =
          ((topLeftOffset.dx - leftEdge) / cellWidth).round();

      // Convert to L/R format
      _needlePositionString =
          _formatNeedleIndex(needleIndexZeroBased, needleCount);

      // Update the controller text ONLY if not focused OR forceUpdate is true
      if (forceUpdateText || !_needleFocus.hasFocus) {
        // Store current selection only if focused (and not forced)
        TextSelection? currentSelection =
            _needleFocus.hasFocus ? _needleController.selection : null;

        // Check if text actually needs changing
        if (_needleController.text != _needlePositionString) {
          _needleController.text = _needlePositionString;
        }

        // Restore selection if it was valid and we were focused
        if (_needleFocus.hasFocus && currentSelection != null) {
          try {
            // Ensure selection is within new text bounds
            final start =
                math.min(currentSelection.start, _needleController.text.length);
            final end =
                math.min(currentSelection.end, _needleController.text.length);
            _needleController.selection =
                TextSelection(baseOffset: start, extentOffset: end);
          } catch (e) {/* Ignore selection errors */} // Added try-catch
        }
      }

      // Update state only if mounted
      if (mounted) {
        // Only call setState if the string actually changed
        // setState(() {}); <-- Removed redundant setState here
      }
    } catch (e, stackTrace) {
      debugPrint("Error calculating needle position: $e\n$stackTrace");
      _needlePositionString = "N/A";
      if (mounted) {
        setState(() {});
      }
    }
  }

  /// Update vertex needle position display when in point edit mode
  void _updateVertexNeedlePositionDisplay(ShapeData shapeData,
      {bool forceUpdateText = false}) {
    try {
      final vertexIndex = _activeVertexIndex;
      if (!_isInVertexEditMode || vertexIndex < 0) {
        return;
      }

      if (vertexIndex >= shapeData.vertices.length) {
        _needlePositionString = "N/A";
        if (mounted) setState(() {});
        return;
      }

      // Don't update text if field is focused (unless forced)
      if (_needleFocus.hasFocus && !forceUpdateText) return;

      final vertex = shapeData.vertices[vertexIndex];
      final gridSystem = widget.controller.gridSystem;
      final screenWidth = widget.screenSize?.width.isFinite ?? false
          ? widget.screenSize!.width
          : Get.width;

      final cellWidth = gridSystem.cellWidth;
      final needleCount = gridSystem.needleCount;
      final totalWidth = needleCount * cellWidth;
      final leftEdge = screenWidth / 2 - (totalWidth / 2);

      if (cellWidth <= 0) {
        _needlePositionString = "N/A";
        if (mounted) setState(() {});
        return;
      }

      // Calculate needle index from vertex position
      final needleIndexZeroBased = ((vertex.dx - leftEdge) / cellWidth).round();

      // Convert to L/R format
      _needlePositionString =
          _formatNeedleIndex(needleIndexZeroBased, needleCount);

      // Update the controller text ONLY if not focused OR forceUpdate is true
      if (forceUpdateText || !_needleFocus.hasFocus) {
        // Store current selection only if focused (and not forced)
        TextSelection? currentSelection =
            _needleFocus.hasFocus ? _needleController.selection : null;

        // Check if text actually needs changing
        if (_needleController.text != _needlePositionString) {
          _needleController.text = _needlePositionString;
        }

        // Restore selection if it was valid and we were focused
        if (_needleFocus.hasFocus && currentSelection != null) {
          try {
            // Ensure selection is within new text bounds
            final start =
                math.min(currentSelection.start, _needleController.text.length);
            final end =
                math.min(currentSelection.end, _needleController.text.length);
            _needleController.selection =
                TextSelection(baseOffset: start, extentOffset: end);
          } catch (e) {/* Ignore selection errors */}
        }
      }

      // Update state only if mounted
      if (mounted) {
        // Only call setState if the string actually changed
        // setState(() {}); <-- Removed redundant setState here
      }
    } catch (e, stackTrace) {
      debugPrint("Error calculating vertex needle position: $e\n$stackTrace");
      _needlePositionString = "N/A";
      if (mounted) {
        setState(() {});
      }
    }
  }

  /// Update edge control needle position display when in edge edit mode
  void _updateEdgeControlNeedlePositionDisplay(ShapeData shapeData,
      {bool forceUpdateText = false}) {
    try {
      final edgeControlInfo = _activeEdgeControl;
      if (!_isInEdgeEditMode || edgeControlInfo == null) {
        return;
      }

      final edgeIndex = edgeControlInfo.edgeIndex;
      final controlIndex = edgeControlInfo.controlIndex;

      if (edgeIndex < 0 || edgeIndex >= shapeData.vertices.length) {
        _needlePositionString = "N/A";
        if (mounted) setState(() {});
        return;
      }

      // Don't update text if field is focused (unless forced)
      if (_needleFocus.hasFocus && !forceUpdateText) return;

      // Get the control point position
      final edgeControls = shapeData.getEdgeCubicControls(edgeIndex);
      if (controlIndex < 0 || controlIndex >= edgeControls.length) {
        _needlePositionString = "N/A";
        if (mounted) setState(() {});
        return;
      }

      // Calculate the actual control point position in world coordinates
      final startVertex = shapeData.vertices[edgeIndex];
      final endVertex =
          shapeData.vertices[(edgeIndex + 1) % shapeData.vertices.length];
      final midpoint = Offset((startVertex.dx + endVertex.dx) / 2,
          (startVertex.dy + endVertex.dy) / 2);
      final controlOffset = edgeControls[controlIndex];
      final controlPoint = midpoint + controlOffset;

      final gridSystem = widget.controller.gridSystem;
      final screenWidth = widget.screenSize?.width.isFinite ?? false
          ? widget.screenSize!.width
          : Get.width;

      final cellWidth = gridSystem.cellWidth;
      final needleCount = gridSystem.needleCount;
      final totalWidth = needleCount * cellWidth;
      final leftEdge = screenWidth / 2 - (totalWidth / 2);

      if (cellWidth <= 0) {
        _needlePositionString = "N/A";
        if (mounted) setState(() {});
        return;
      }

      // Calculate needle index from control point position
      final needleIndexZeroBased =
          ((controlPoint.dx - leftEdge) / cellWidth).round();

      // Convert to L/R format
      _needlePositionString =
          _formatNeedleIndex(needleIndexZeroBased, needleCount);

      // Update the controller text ONLY if not focused OR forceUpdate is true
      if (forceUpdateText || !_needleFocus.hasFocus) {
        // Store current selection only if focused (and not forced)
        TextSelection? currentSelection =
            _needleFocus.hasFocus ? _needleController.selection : null;

        // Check if text actually needs changing
        if (_needleController.text != _needlePositionString) {
          _needleController.text = _needlePositionString;
        }

        // Restore selection if it was valid and we were focused
        if (_needleFocus.hasFocus && currentSelection != null) {
          try {
            // Ensure selection is within new text bounds
            final start =
                math.min(currentSelection.start, _needleController.text.length);
            final end =
                math.min(currentSelection.end, _needleController.text.length);
            _needleController.selection =
                TextSelection(baseOffset: start, extentOffset: end);
          } catch (e) {/* Ignore selection errors */}
        }
      }
    } catch (e, stackTrace) {
      debugPrint(
          "Error calculating edge control needle position: $e\n$stackTrace");
      _needlePositionString = "N/A";
      if (mounted) {
        setState(() {});
      }
    }
  }

  /// Update vertex row position display when in point edit mode
  void _updateVertexRowPositionDisplay(ShapeData shapeData,
      {bool forceUpdateText = false}) {
    try {
      final vertexIndex = _activeVertexIndex;
      if (!_isInVertexEditMode || vertexIndex < 0) {
        return;
      }

      if (vertexIndex >= shapeData.vertices.length) {
        _rowPositionString = "1";
        if (mounted) setState(() {});
        return;
      }

      // Don't update text if field is focused (unless forced)
      if (_rowFocus.hasFocus && !forceUpdateText) return;

      final vertex = shapeData.vertices[vertexIndex];
      final gridSystem = widget.controller.gridSystem;
      final cellWidth = gridSystem.cellWidth;
      final aspectRatio = widget.controller.aspectRatio.value;
      final cellHeight = cellWidth * aspectRatio;

      if (cellHeight <= 0) {
        _rowPositionString = "1";
        if (mounted) setState(() {});
        return;
      }

      // Calculate row number from vertex position (1-based)
      final rowIndex = (vertex.dy / cellHeight).round() + 1;

      // Ensure positive value
      _rowPositionString = math.max(1, rowIndex).toString();

      // Update the controller text ONLY if not focused OR forceUpdate is true
      if (forceUpdateText || !_rowFocus.hasFocus) {
        // Store current selection only if focused (and not forced)
        TextSelection? currentSelection =
            _rowFocus.hasFocus ? _rowController.selection : null;

        // Check if text actually needs changing
        if (_rowController.text != _rowPositionString) {
          _rowController.text = _rowPositionString;
        }

        // Restore selection if it was valid and we were focused
        if (_rowFocus.hasFocus && currentSelection != null) {
          try {
            // Ensure selection is within new text bounds
            final start =
                math.min(currentSelection.start, _rowController.text.length);
            final end =
                math.min(currentSelection.end, _rowController.text.length);
            _rowController.selection =
                TextSelection(baseOffset: start, extentOffset: end);
          } catch (e) {/* Ignore selection errors */}
        }
      }
    } catch (e, stackTrace) {
      debugPrint("Error calculating vertex row position: $e\n$stackTrace");
      _rowPositionString = "1";
      if (mounted) {
        setState(() {});
      }
    }
  }

  /// Update edge control row position display when in edge edit mode
  void _updateEdgeControlRowPositionDisplay(ShapeData shapeData,
      {bool forceUpdateText = false}) {
    try {
      final edgeControlInfo = _activeEdgeControl;
      if (!_isInEdgeEditMode || edgeControlInfo == null) {
        return;
      }

      final edgeIndex = edgeControlInfo.edgeIndex;
      final controlIndex = edgeControlInfo.controlIndex;

      if (edgeIndex < 0 || edgeIndex >= shapeData.vertices.length) {
        _rowPositionString = "1";
        if (mounted) setState(() {});
        return;
      }

      // Don't update text if field is focused (unless forced)
      if (_rowFocus.hasFocus && !forceUpdateText) return;

      // Get the control point position
      final edgeControls = shapeData.getEdgeCubicControls(edgeIndex);
      if (controlIndex < 0 || controlIndex >= edgeControls.length) {
        _rowPositionString = "1";
        if (mounted) setState(() {});
        return;
      }

      // Calculate the actual control point position in world coordinates
      final startVertex = shapeData.vertices[edgeIndex];
      final endVertex =
          shapeData.vertices[(edgeIndex + 1) % shapeData.vertices.length];
      final midpoint = Offset((startVertex.dx + endVertex.dx) / 2,
          (startVertex.dy + endVertex.dy) / 2);
      final controlOffset = edgeControls[controlIndex];
      final controlPoint = midpoint + controlOffset;

      final gridSystem = widget.controller.gridSystem;
      final cellWidth = gridSystem.cellWidth;
      final aspectRatio = widget.controller.aspectRatio.value;
      final cellHeight = cellWidth * aspectRatio;

      if (cellHeight <= 0) {
        _rowPositionString = "1";
        if (mounted) setState(() {});
        return;
      }

      // Calculate row number from control point position (1-based)
      final rowIndex = (controlPoint.dy / cellHeight).round() + 1;

      // Ensure positive value
      _rowPositionString = math.max(1, rowIndex).toString();

      // Update the controller text ONLY if not focused OR forceUpdate is true
      if (forceUpdateText || !_rowFocus.hasFocus) {
        // Store current selection only if focused (and not forced)
        TextSelection? currentSelection =
            _rowFocus.hasFocus ? _rowController.selection : null;

        // Check if text actually needs changing
        if (_rowController.text != _rowPositionString) {
          _rowController.text = _rowPositionString;
        }

        // Restore selection if it was valid and we were focused
        if (_rowFocus.hasFocus && currentSelection != null) {
          try {
            // Ensure selection is within new text bounds
            final start =
                math.min(currentSelection.start, _rowController.text.length);
            final end =
                math.min(currentSelection.end, _rowController.text.length);
            _rowController.selection =
                TextSelection(baseOffset: start, extentOffset: end);
          } catch (e) {/* Ignore selection errors */}
        }
      }
    } catch (e, stackTrace) {
      debugPrint(
          "Error calculating edge control row position: $e\n$stackTrace");
      _rowPositionString = "1";
      if (mounted) {
        setState(() {});
      }
    }
  }

  // --- Helper to format needle index ---
  String _formatNeedleIndex(int index, int totalNeedles) {
    final centerIndex = (totalNeedles / 2).floor();
    final relativeIndex = index - centerIndex;

    if (relativeIndex == 0) {
      return "0";
    } else if (relativeIndex < 0) {
      return "L${relativeIndex.abs()}";
    } else {
      return "R$relativeIndex";
    }
  }

  // --- Helper to parse formatted needle string --- (Returns null if invalid)
  int? _parseNeedleString(String needleStr, int totalNeedles) {
    final centerIndex = (totalNeedles / 2).floor();
    if (needleStr == "0") {
      return centerIndex;
    }
    try {
      if (needleStr.startsWith('L')) {
        final number = int.parse(needleStr.substring(1));
        return centerIndex - number;
      } else if (needleStr.startsWith('R')) {
        final number = int.parse(needleStr.substring(1));
        return centerIndex + number;
      } else {
        return null; // Invalid format
      }
    } catch (e) {
      return null; // Parsing error
    }
  }

  // --- Method to get Screen X from 0-based needle index ---
  double _getScreenXFromNeedleIndex(
      int index, int totalNeedles, double cellW, double screenW) {
    final totalGridWidth = totalNeedles * cellW;
    final leftEdge = screenW / 2 - (totalGridWidth / 2);
    return leftEdge + (index * cellW);
  }

  // --- Updated Method to Adjust Needle Position (handles focus) ---
  void _adjustNeedlePosition(int delta) {
    try {
      // Check if we're in vertex edit mode
      if (_isInVertexEditMode) {
        _adjustVertexNeedlePosition(delta);
        return;
      }

      // Check if we're in edge edit mode
      if (_isInEdgeEditMode) {
        _adjustEdgeControlNeedlePosition(delta);
        return;
      }

      widget.controller.startHistoryTracking("Adjust Needle Position");

      // 1. Get current shape data and bounding box
      final currentData = widget.shapeData?.deepCopy();
      if (currentData == null) {
        widget.controller.finishHistoryTracking(); // Abort history
        return;
      }
      final dynamic boundsResult =
          GeometryUtils.calculateAccurateBoundingRect(currentData);
      Rect currentRect;
      if (boundsResult is GroupBoundsData) {
        currentRect = boundsResult.bounds;
      } else if (boundsResult is Rect) {
        currentRect = boundsResult;
      } else {
        widget.controller.finishHistoryTracking(); // Abort history
        return;
      }
      final currentTopLeft = currentRect.topLeft;
      final gridSystem = widget.controller.gridSystem;
      final screenWidth = widget.screenSize?.width ?? Get.width;
      final cellWidth = gridSystem.cellWidth;
      final needleCount = gridSystem.needleCount;

      // Check for valid cellWidth
      if (cellWidth <= 0) {
        debugPrint(
            "Cannot adjust needle position: Invalid cellWidth ($cellWidth)");
        widget.controller.finishHistoryTracking();
        return;
      }

      // 2. Get current 0-based needle index
      final totalGridWidth = needleCount * cellWidth;
      final leftEdge = screenWidth / 2 - (totalGridWidth / 2);
      final currentNeedleIndexZeroBased =
          ((currentTopLeft.dx - leftEdge) / cellWidth).round();

      // 3. Calculate target 0-based needle index and check limits
      final targetNeedleIndexZeroBased = currentNeedleIndexZeroBased + delta;

      // Clamp the target needle index (0 to needleCount)
      final clampedNeedleIndexZeroBased =
          targetNeedleIndexZeroBased.clamp(0, needleCount);

      // If clamped value is same as current, no move needed
      if (clampedNeedleIndexZeroBased == currentNeedleIndexZeroBased) {
        widget.controller.finishHistoryTracking(); // Abort history (no change)
        return;
      }

      // 4. Convert target needle index back to screen X coordinate
      final targetDx = _getScreenXFromNeedleIndex(
          clampedNeedleIndexZeroBased, needleCount, cellWidth, screenWidth);

      // 5. Calculate translation delta
      final translationDeltaX = targetDx - currentTopLeft.dx;
      final translationDelta = Offset(translationDeltaX, 0);

      // 6. Apply translation to the shape data
      ShapeData translatedShapeData;
      if (currentData is GroupShapeData) {
        final groupData = currentData;
        // Translate group vertices and center
        final newVertices =
            groupData.vertices.map((v) => v + translationDelta).toList();
        final newCenter = groupData.center + translationDelta;
        // Translate child shapes efficiently
        final updatedChildShapes = groupData.transformChildShapes(
          translation: translationDelta,
        );
        // Create new group data
        final newGroupData = groupData.copyWith(
          vertices: newVertices,
          center: newCenter,
          boundingRect: groupData.boundingRect.shift(translationDelta),
        ) as GroupShapeData;
        translatedShapeData = newGroupData.copyWithChildren(
          childShapes: updatedChildShapes,
        );
      } else {
        // Regular shape translation
        final newVertices =
            currentData.vertices.map((v) => v + translationDelta).toList();
        final newCenter = currentData.center + translationDelta;
        translatedShapeData = currentData.copyWith(
          vertices: newVertices,
          center: newCenter,
          boundingRect: currentData.boundingRect.shift(translationDelta),
        );
      }

      // 7. Apply constraints (boundaries, mirror mode)
      final boardSize = _getBoardSize(); // Get current board size
      final boundaryConstrainedData =
          _constrainShapeWithinBoundaries(translatedShapeData, boardSize);

      ShapeData finalConstrainedData = boundaryConstrainedData;
      if (widget.controller.isMirrorModeActive.value &&
          widget.shapeKey != null) {
        final mirrorConstrainedData = widget.controller
            .constrainShapePosition(widget.shapeKey!, boundaryConstrainedData);
        if (mirrorConstrainedData != null) {
          finalConstrainedData = mirrorConstrainedData;
        }
      }

      // 8. Save state
      if (widget.shapeKey != null) {
        widget.controller
            .saveShapeState(widget.shapeKey!, finalConstrainedData);
        widget.controller.finishHistoryTracking();

        // 9. Force UI update (shape and HUD display)
        _forceRebuildShape(widget.shapeKey!, finalConstrainedData);
        // Update HUD text only if field not focused
        if (!_needleFocus.hasFocus) {
          _updateNeedlePositionDisplay();
        }
      } else {
        widget.controller.finishHistoryTracking();
      }
    } catch (e, stackTrace) {
      debugPrint("Error adjusting needle position: $e\n$stackTrace");
      widget.controller
          .finishHistoryTracking(); // Ensure history tracking ends on error
    }
  }

  /// Adjust edge control needle position incrementally
  void _adjustEdgeControlNeedlePosition(int delta) {
    try {
      final edgeControlInfo = _activeEdgeControl;
      if (!_isInEdgeEditMode || edgeControlInfo == null) return;
      if (widget.shapeKey == null) return;

      widget.controller
          .startHistoryTracking("Adjust Edge Control Needle Position");

      final edgeIndex = edgeControlInfo.edgeIndex;
      final controlIndex = edgeControlInfo.controlIndex;

      // Get current shape data
      final currentData =
          widget.controller.getShapeState(widget.shapeKey)?.deepCopy();
      if (currentData == null) {
        widget.controller.finishHistoryTracking();
        return;
      }

      if (edgeIndex < 0 || edgeIndex >= currentData.vertices.length) {
        widget.controller.finishHistoryTracking();
        return;
      }

      // Get the current control point offset
      final edgeControls = currentData.getEdgeCubicControls(edgeIndex);
      if (controlIndex < 0 || controlIndex >= edgeControls.length) {
        widget.controller.finishHistoryTracking();
        return;
      }

      final currentControlOffset = edgeControls[controlIndex];

      // Calculate current control point world position
      final startVertex = currentData.vertices[edgeIndex];
      final endVertex =
          currentData.vertices[(edgeIndex + 1) % currentData.vertices.length];
      final midpoint = Offset((startVertex.dx + endVertex.dx) / 2,
          (startVertex.dy + endVertex.dy) / 2);
      final currentControlPoint = midpoint + currentControlOffset;

      final gridSystem = widget.controller.gridSystem;
      final screenWidth = widget.screenSize?.width != null
          ? widget.screenSize!.width
          : Get.width;
      final cellWidth = gridSystem.cellWidth;
      final needleCount = gridSystem.needleCount;

      if (cellWidth <= 0) {
        widget.controller.finishHistoryTracking();
        return;
      }

      // Calculate current needle index from control point position
      final totalGridWidth = needleCount * cellWidth;
      final leftEdge = screenWidth / 2 - (totalGridWidth / 2);
      final currentNeedleIndexZeroBased =
          ((currentControlPoint.dx - leftEdge) / cellWidth).round();

      // Calculate target needle index
      final targetNeedleIndexZeroBased = currentNeedleIndexZeroBased + delta;
      final clampedTargetIndex =
          targetNeedleIndexZeroBased.clamp(0, needleCount);

      // If no change needed, abort
      if (clampedTargetIndex == currentNeedleIndexZeroBased) {
        widget.controller.finishHistoryTracking();
        return;
      }

      // Convert target needle index to screen X coordinate
      final targetDx = _getScreenXFromNeedleIndex(
          clampedTargetIndex, needleCount, cellWidth, screenWidth);

      // Create new control point position (only update X coordinate)
      final newControlPoint = Offset(targetDx, currentControlPoint.dy);

      // Calculate new control offset relative to edge midpoint
      final newControlOffset = newControlPoint - midpoint;

      // Update the control point WITH history tracking
      widget.controller.updateCubicCurveControl(
          widget.shapeKey!, edgeIndex, controlIndex, newControlOffset);

      widget.controller.finishHistoryTracking();

      // Update HUD display if not focused
      if (!_needleFocus.hasFocus) {
        _updateNeedlePositionDisplay();
      }
    } catch (e, stackTrace) {
      debugPrint(
          "Error adjusting edge control needle position: $e\n$stackTrace");
      widget.controller.finishHistoryTracking();
    }
  }

  /// Adjust edge control row position incrementally
  void _adjustEdgeControlRowPosition(int delta) {
    try {
      final edgeControlInfo = _activeEdgeControl;
      if (!_isInEdgeEditMode || edgeControlInfo == null) return;
      if (widget.shapeKey == null) return;

      widget.controller
          .startHistoryTracking("Adjust Edge Control Row Position");

      final edgeIndex = edgeControlInfo.edgeIndex;
      final controlIndex = edgeControlInfo.controlIndex;

      // Get current shape data
      final currentData =
          widget.controller.getShapeState(widget.shapeKey)?.deepCopy();
      if (currentData == null) {
        widget.controller.finishHistoryTracking();
        return;
      }

      if (edgeIndex < 0 || edgeIndex >= currentData.vertices.length) {
        widget.controller.finishHistoryTracking();
        return;
      }

      // Get the current control point offset
      final edgeControls = currentData.getEdgeCubicControls(edgeIndex);
      if (controlIndex < 0 || controlIndex >= edgeControls.length) {
        widget.controller.finishHistoryTracking();
        return;
      }

      final currentControlOffset = edgeControls[controlIndex];

      // Calculate current control point world position
      final startVertex = currentData.vertices[edgeIndex];
      final endVertex =
          currentData.vertices[(edgeIndex + 1) % currentData.vertices.length];
      final midpoint = Offset((startVertex.dx + endVertex.dx) / 2,
          (startVertex.dy + endVertex.dy) / 2);
      final currentControlPoint = midpoint + currentControlOffset;

      // Get grid properties
      final gridSystem = widget.controller.gridSystem;
      final cellWidth = gridSystem.cellWidth;
      final aspectRatio = widget.controller.aspectRatio.value;
      final cellHeight = cellWidth * aspectRatio;

      if (cellHeight <= 0) {
        widget.controller.finishHistoryTracking();
        return;
      }

      // Calculate current row from control point position (1-based)
      final currentRow = (currentControlPoint.dy / cellHeight).round() + 1;

      // Calculate target row
      final targetRow = currentRow + delta;
      final clampedTargetRow = math.max(1, targetRow);

      // If no change needed, abort
      if (clampedTargetRow == currentRow) {
        widget.controller.finishHistoryTracking();
        return;
      }

      // Calculate target Y position (convert from 1-based to 0-based for calculation)
      final targetY = (clampedTargetRow - 1) * cellHeight;

      // Create new control point position (only update Y coordinate)
      final newControlPoint = Offset(currentControlPoint.dx, targetY);

      // Calculate new control offset relative to edge midpoint
      final newControlOffset = newControlPoint - midpoint;

      // Update the control point WITH history tracking
      widget.controller.updateCubicCurveControl(
          widget.shapeKey!, edgeIndex, controlIndex, newControlOffset);

      widget.controller.finishHistoryTracking();

      // Update HUD display if not focused
      if (!_rowFocus.hasFocus) {
        _updateRowPositionDisplay();
      }
    } catch (e, stackTrace) {
      debugPrint("Error adjusting edge control row position: $e\n$stackTrace");
      widget.controller.finishHistoryTracking();
    }
  }

  // --- Method to adjust row position incrementally ---
  void _adjustRowPosition(int delta) {
    try {
      // Check if we're in vertex edit mode
      if (_isInVertexEditMode) {
        _adjustVertexRowPosition(delta);
        return;
      }

      // Check if we're in edge edit mode
      if (_isInEdgeEditMode) {
        _adjustEdgeControlRowPosition(delta);
        return;
      }

      widget.controller.startHistoryTracking("Adjust Row Position");

      // ... rest of existing method for default mode
      // Get current shape data
      final currentData = widget.shapeData?.deepCopy();
      if (currentData == null) {
        widget.controller.finishHistoryTracking();
        return;
      }

      // Get current bounding box
      final dynamic boundsResult =
          GeometryUtils.calculateAccurateBoundingRect(currentData);
      Rect currentRect;
      if (boundsResult is GroupBoundsData) {
        currentRect = boundsResult.bounds;
      } else if (boundsResult is Rect) {
        currentRect = boundsResult;
      } else {
        widget.controller.finishHistoryTracking();
        return;
      }

      final currentTopLeft = currentRect.topLeft;

      // Get grid properties
      final gridSystem = widget.controller.gridSystem;
      final cellWidth = gridSystem.cellWidth;
      final aspectRatio = widget.controller.aspectRatio.value;
      final cellHeight = cellWidth * aspectRatio;

      if (cellHeight <= 0) {
        widget.controller.finishHistoryTracking();
        return;
      }

      // Calculate current row (1-based)
      final currentRow = (currentTopLeft.dy / cellHeight).round() + 1;

      // Calculate target row (1-based)
      final targetRow = currentRow + delta;

      // Ensure target row is positive
      final clampedTargetRow = math.max(1, targetRow);

      // If no change needed, abort history
      if (clampedTargetRow == currentRow) {
        widget.controller.finishHistoryTracking();
        return;
      }

      // Call _setRowPosition to handle the actual positioning
      _setRowPosition(clampedTargetRow, trackHistory: false);

      // Finish history tracking here
      widget.controller.finishHistoryTracking();
    } catch (e, stackTrace) {
      debugPrint("Error adjusting row position: $e\n$stackTrace");
      widget.controller.finishHistoryTracking();
    }
  }

  // --- Vertex Position Update Methods ---

  /// Update vertex position horizontally (needle position) in real-time
  void _updateVertexPositionRealtime(int targetNeedleIndexZeroBased,
      {required bool isHorizontal}) {
    try {
      final vertexIndex = _activeVertexIndex;
      if (!_isInVertexEditMode || vertexIndex < 0) return;
      if (widget.shapeKey == null) return;

      // Get current shape data
      final currentData =
          widget.controller.getShapeState(widget.shapeKey)?.deepCopy();
      if (currentData == null) return;

      if (vertexIndex >= currentData.vertices.length) return;

      // Don't allow vertex editing on group shapes
      if (currentData.type == ShapeType.group ||
          currentData is GroupShapeData) {
        return;
      }

      final currentVertex = currentData.vertices[vertexIndex];
      final gridSystem = widget.controller.gridSystem;
      final screenWidth = widget.screenSize?.width != null
          ? widget.screenSize!.width
          : Get.width;
      final cellWidth = gridSystem.cellWidth;
      final needleCount = gridSystem.needleCount;

      if (cellWidth <= 0) return;

      // Clamp target index
      final clampedTargetIndex =
          targetNeedleIndexZeroBased.clamp(0, needleCount);

      // Convert target needle index to screen X coordinate
      final targetDx = _getScreenXFromNeedleIndex(
          clampedTargetIndex, needleCount, cellWidth, screenWidth);

      // Create new vertex position (only update X coordinate)
      final newVertex = Offset(targetDx, currentVertex.dy);

      // Update the vertex in the shape
      final updatedVertices = List<Offset>.from(currentData.vertices);
      updatedVertices[vertexIndex] = newVertex;

      // Recalculate shape data after vertex edit
      final updatedShapeData =
          _recalculateShapeDataAfterVertexEdit(currentData, updatedVertices);

      // Save state WITHOUT history tracking (real-time update)
      widget.controller.saveShapeState(widget.shapeKey!, updatedShapeData);

      // Force UI update
      _forceRebuildShape(widget.shapeKey!, updatedShapeData);
    } catch (e, stackTrace) {
      debugPrint("Error updating vertex position real-time: $e\n$stackTrace");
      if (mounted && _needlePositionError == null) {
        setState(() {
          _needlePositionError =
              'shapeEditor_propertyHud_errors_positionGeneric'.tr;
        });
      }
    }
  }

  /// Update vertex position vertically (row position) in real-time
  void _updateVertexRowPositionRealtime(int rowNumber) {
    try {
      final vertexIndex = _activeVertexIndex;
      if (!_isInVertexEditMode || vertexIndex < 0) return;
      if (widget.shapeKey == null) return;

      // Get current shape data
      final currentData =
          widget.controller.getShapeState(widget.shapeKey)?.deepCopy();
      if (currentData == null) return;

      if (vertexIndex >= currentData.vertices.length) return;

      // Don't allow vertex editing on group shapes
      if (currentData.type == ShapeType.group ||
          currentData is GroupShapeData) {
        return;
      }

      final currentVertex = currentData.vertices[vertexIndex];

      // Get grid properties
      final gridSystem = widget.controller.gridSystem;
      final cellWidth = gridSystem.cellWidth;
      final aspectRatio = widget.controller.aspectRatio.value;
      final cellHeight = cellWidth * aspectRatio;

      if (cellHeight <= 0) return;

      // Calculate target Y position (convert from 1-based to 0-based for calculation)
      final targetY = (rowNumber - 1) * cellHeight;

      // Create new vertex position (only update Y coordinate)
      final newVertex = Offset(currentVertex.dx, targetY);

      // Update the vertex in the shape
      final updatedVertices = List<Offset>.from(currentData.vertices);
      updatedVertices[vertexIndex] = newVertex;

      // Recalculate shape data after vertex edit
      final updatedShapeData =
          _recalculateShapeDataAfterVertexEdit(currentData, updatedVertices);

      // Save state WITHOUT history tracking (real-time update)
      widget.controller.saveShapeState(widget.shapeKey!, updatedShapeData);

      // Force UI update
      _forceRebuildShape(widget.shapeKey!, updatedShapeData);
    } catch (e, stackTrace) {
      debugPrint(
          "Error updating vertex row position real-time: $e\n$stackTrace");
      if (mounted && _rowPositionError == null) {
        setState(() {
          _rowPositionError =
              'shapeEditor_propertyHud_errors_positionGeneric'.tr;
        });
      }
    }
  }

  /// Update edge control position horizontally (needle position) in real-time
  void _updateEdgeControlPositionRealtime(int targetNeedleIndexZeroBased,
      {required bool isHorizontal}) {
    try {
      final edgeControlInfo = _activeEdgeControl;
      if (!_isInEdgeEditMode || edgeControlInfo == null) return;
      if (widget.shapeKey == null) return;

      final edgeIndex = edgeControlInfo.edgeIndex;
      final controlIndex = edgeControlInfo.controlIndex;

      // Get current shape data
      final currentData =
          widget.controller.getShapeState(widget.shapeKey)?.deepCopy();
      if (currentData == null) return;

      if (edgeIndex < 0 || edgeIndex >= currentData.vertices.length) return;

      // Get the current control point offset
      final edgeControls = currentData.getEdgeCubicControls(edgeIndex);
      if (controlIndex < 0 || controlIndex >= edgeControls.length) return;

      final currentControlOffset = edgeControls[controlIndex];

      // Calculate current control point world position
      final startVertex = currentData.vertices[edgeIndex];
      final endVertex =
          currentData.vertices[(edgeIndex + 1) % currentData.vertices.length];
      final midpoint = Offset((startVertex.dx + endVertex.dx) / 2,
          (startVertex.dy + endVertex.dy) / 2);
      final currentControlPoint = midpoint + currentControlOffset;

      final gridSystem = widget.controller.gridSystem;
      final screenWidth = widget.screenSize?.width != null
          ? widget.screenSize!.width
          : Get.width;
      final cellWidth = gridSystem.cellWidth;
      final needleCount = gridSystem.needleCount;

      if (cellWidth <= 0) return;

      // Clamp target index
      final clampedTargetIndex =
          targetNeedleIndexZeroBased.clamp(0, needleCount);

      // Convert target needle index to screen X coordinate
      final targetDx = _getScreenXFromNeedleIndex(
          clampedTargetIndex, needleCount, cellWidth, screenWidth);

      // Create new control point position (only update X coordinate)
      final newControlPoint = Offset(targetDx, currentControlPoint.dy);

      // Calculate new control offset relative to edge midpoint
      final newControlOffset = newControlPoint - midpoint;

      // Update the control point
      widget.controller.updateCubicCurveControl(
          widget.shapeKey!, edgeIndex, controlIndex, newControlOffset);
    } catch (e, stackTrace) {
      debugPrint(
          "Error updating edge control position real-time: $e\n$stackTrace");
      if (mounted && _needlePositionError == null) {
        setState(() {
          _needlePositionError =
              'shapeEditor_propertyHud_errors_positionGeneric'.tr;
        });
      }
    }
  }

  /// Update edge control position vertically (row position) in real-time
  void _updateEdgeControlRowPositionRealtime(int rowNumber) {
    try {
      final edgeControlInfo = _activeEdgeControl;
      if (!_isInEdgeEditMode || edgeControlInfo == null) return;
      if (widget.shapeKey == null) return;

      final edgeIndex = edgeControlInfo.edgeIndex;
      final controlIndex = edgeControlInfo.controlIndex;

      // Get current shape data
      final currentData =
          widget.controller.getShapeState(widget.shapeKey)?.deepCopy();
      if (currentData == null) return;

      if (edgeIndex < 0 || edgeIndex >= currentData.vertices.length) return;

      // Get the current control point offset
      final edgeControls = currentData.getEdgeCubicControls(edgeIndex);
      if (controlIndex < 0 || controlIndex >= edgeControls.length) return;

      final currentControlOffset = edgeControls[controlIndex];

      // Calculate current control point world position
      final startVertex = currentData.vertices[edgeIndex];
      final endVertex =
          currentData.vertices[(edgeIndex + 1) % currentData.vertices.length];
      final midpoint = Offset((startVertex.dx + endVertex.dx) / 2,
          (startVertex.dy + endVertex.dy) / 2);
      final currentControlPoint = midpoint + currentControlOffset;

      // Get grid properties
      final gridSystem = widget.controller.gridSystem;
      final cellWidth = gridSystem.cellWidth;
      final aspectRatio = widget.controller.aspectRatio.value;
      final cellHeight = cellWidth * aspectRatio;

      if (cellHeight <= 0) return;

      // Calculate target Y position (convert from 1-based to 0-based for calculation)
      final targetY = (rowNumber - 1) * cellHeight;

      // Create new control point position (only update Y coordinate)
      final newControlPoint = Offset(currentControlPoint.dx, targetY);

      // Calculate new control offset relative to edge midpoint
      final newControlOffset = newControlPoint - midpoint;

      // Update the control point
      widget.controller.updateCubicCurveControl(
          widget.shapeKey!, edgeIndex, controlIndex, newControlOffset);
    } catch (e, stackTrace) {
      debugPrint(
          "Error updating edge control row position real-time: $e\n$stackTrace");
      if (mounted && _rowPositionError == null) {
        setState(() {
          _rowPositionError =
              'shapeEditor_propertyHud_errors_positionGeneric'.tr;
        });
      }
    }
  }

  /// Helper method to recalculate shape data after vertex editing
  ShapeData _recalculateShapeDataAfterVertexEdit(
      ShapeData originalData, List<Offset> newVertices) {
    // Calculate new bounding box
    double minX = double.infinity;
    double minY = double.infinity;
    double maxX = -double.infinity;
    double maxY = -double.infinity;

    for (final vertex in newVertices) {
      minX = math.min(minX, vertex.dx);
      minY = math.min(minY, vertex.dy);
      maxX = math.max(maxX, vertex.dx);
      maxY = math.max(maxY, vertex.dy);
    }

    final newBoundingRect = Rect.fromLTRB(minX, minY, maxX, maxY);
    final newCenter = Offset(
      newBoundingRect.left + newBoundingRect.width / 2,
      newBoundingRect.top + newBoundingRect.height / 2,
    );

    return originalData.copyWith(
      vertices: newVertices,
      boundingRect: newBoundingRect,
      center: newCenter,
    );
  }

  /// Adjust vertex needle position incrementally
  void _adjustVertexNeedlePosition(int delta) {
    try {
      final vertexIndex = _activeVertexIndex;
      if (!_isInVertexEditMode || vertexIndex < 0) return;
      if (widget.shapeKey == null) return;

      widget.controller.startHistoryTracking("Adjust Vertex Needle Position");

      // Get current shape data
      final currentData =
          widget.controller.getShapeState(widget.shapeKey)?.deepCopy();
      if (currentData == null) {
        widget.controller.finishHistoryTracking();
        return;
      }

      if (vertexIndex >= currentData.vertices.length) {
        widget.controller.finishHistoryTracking();
        return;
      }

      // Don't allow vertex editing on group shapes
      if (currentData.type == ShapeType.group ||
          currentData is GroupShapeData) {
        widget.controller.finishHistoryTracking();
        return;
      }

      final currentVertex = currentData.vertices[vertexIndex];
      final gridSystem = widget.controller.gridSystem;
      final screenWidth = widget.screenSize?.width != null
          ? widget.screenSize!.width
          : Get.width;
      final cellWidth = gridSystem.cellWidth;
      final needleCount = gridSystem.needleCount;

      if (cellWidth <= 0) {
        widget.controller.finishHistoryTracking();
        return;
      }

      // Calculate current needle index from vertex position
      final totalGridWidth = needleCount * cellWidth;
      final leftEdge = screenWidth / 2 - (totalGridWidth / 2);
      final currentNeedleIndexZeroBased =
          ((currentVertex.dx - leftEdge) / cellWidth).round();

      // Calculate target needle index
      final targetNeedleIndexZeroBased = currentNeedleIndexZeroBased + delta;
      final clampedTargetIndex =
          targetNeedleIndexZeroBased.clamp(0, needleCount);

      // If no change needed, abort
      if (clampedTargetIndex == currentNeedleIndexZeroBased) {
        widget.controller.finishHistoryTracking();
        return;
      }

      // Convert target needle index to screen X coordinate
      final targetDx = _getScreenXFromNeedleIndex(
          clampedTargetIndex, needleCount, cellWidth, screenWidth);

      // Create new vertex position
      final newVertex = Offset(targetDx, currentVertex.dy);

      // Update the vertex in the shape
      final updatedVertices = List<Offset>.from(currentData.vertices);
      updatedVertices[vertexIndex] = newVertex;

      // Recalculate shape data after vertex edit
      final updatedShapeData =
          _recalculateShapeDataAfterVertexEdit(currentData, updatedVertices);

      // Save state WITH history tracking
      widget.controller.saveShapeState(widget.shapeKey!, updatedShapeData);
      widget.controller.finishHistoryTracking();

      // Force UI update
      _forceRebuildShape(widget.shapeKey!, updatedShapeData);

      // Update HUD display if not focused
      if (!_needleFocus.hasFocus) {
        _updateNeedlePositionDisplay();
      }
    } catch (e, stackTrace) {
      debugPrint("Error adjusting vertex needle position: $e\n$stackTrace");
      widget.controller.finishHistoryTracking();
    }
  }

  /// Adjust vertex row position incrementally
  void _adjustVertexRowPosition(int delta) {
    try {
      final vertexIndex = _activeVertexIndex;
      if (!_isInVertexEditMode || vertexIndex < 0) return;
      if (widget.shapeKey == null) return;

      widget.controller.startHistoryTracking("Adjust Vertex Row Position");

      // Get current shape data
      final currentData =
          widget.controller.getShapeState(widget.shapeKey)?.deepCopy();
      if (currentData == null) {
        widget.controller.finishHistoryTracking();
        return;
      }

      if (vertexIndex >= currentData.vertices.length) {
        widget.controller.finishHistoryTracking();
        return;
      }

      // Don't allow vertex editing on group shapes
      if (currentData.type == ShapeType.group ||
          currentData is GroupShapeData) {
        widget.controller.finishHistoryTracking();
        return;
      }

      final currentVertex = currentData.vertices[vertexIndex];

      // Get grid properties
      final gridSystem = widget.controller.gridSystem;
      final cellWidth = gridSystem.cellWidth;
      final aspectRatio = widget.controller.aspectRatio.value;
      final cellHeight = cellWidth * aspectRatio;

      if (cellHeight <= 0) {
        widget.controller.finishHistoryTracking();
        return;
      }

      // Calculate current row from vertex position (1-based)
      final currentRow = (currentVertex.dy / cellHeight).round() + 1;

      // Calculate target row
      final targetRow = currentRow + delta;
      final clampedTargetRow = math.max(1, targetRow);

      // If no change needed, abort
      if (clampedTargetRow == currentRow) {
        widget.controller.finishHistoryTracking();
        return;
      }

      // Calculate target Y position (convert from 1-based to 0-based for calculation)
      final targetY = (clampedTargetRow - 1) * cellHeight;

      // Create new vertex position
      final newVertex = Offset(currentVertex.dx, targetY);

      // Update the vertex in the shape
      final updatedVertices = List<Offset>.from(currentData.vertices);
      updatedVertices[vertexIndex] = newVertex;

      // Recalculate shape data after vertex edit
      final updatedShapeData =
          _recalculateShapeDataAfterVertexEdit(currentData, updatedVertices);

      // Save state WITH history tracking
      widget.controller.saveShapeState(widget.shapeKey!, updatedShapeData);
      widget.controller.finishHistoryTracking();

      // Force UI update
      _forceRebuildShape(widget.shapeKey!, updatedShapeData);

      // Update HUD display if not focused
      if (!_rowFocus.hasFocus) {
        _updateRowPositionDisplay();
      }
    } catch (e, stackTrace) {
      debugPrint("Error adjusting vertex row position: $e\n$stackTrace");
      widget.controller.finishHistoryTracking();
    }
  }

  /// New method to update shape position realtime (from onChanged)
  void _updateShapePositionRealtime(int targetNeedleIndexZeroBased) {
    try {
      if (widget.shapeKey == null) return;

      // 1. Get current shape data and bounding box
      final currentData =
          widget.controller.getShapeState(widget.shapeKey)?.deepCopy();
      if (currentData == null) return;

      final dynamic boundsResult =
          GeometryUtils.calculateAccurateBoundingRect(currentData);
      Rect currentRect;
      if (boundsResult is GroupBoundsData) {
        currentRect = boundsResult.bounds;
      } else if (boundsResult is Rect) {
        currentRect = boundsResult;
      } else {
        return;
      }

      final currentTopLeft = currentRect.topLeft;
      final gridSystem = widget.controller.gridSystem;
      final screenWidth = widget.screenSize?.width != null
          ? widget.screenSize!.width
          : Get.width;
      final cellWidth = gridSystem.cellWidth;
      final needleCount = gridSystem.needleCount;

      if (cellWidth <= 0) return;

      // 2. Clamp target index (already parsed)
      final clampedTargetIndex =
          targetNeedleIndexZeroBased.clamp(0, needleCount);

      // 3. Convert target needle index back to screen X coordinate
      final targetDx = _getScreenXFromNeedleIndex(
          clampedTargetIndex, needleCount, cellWidth, screenWidth);

      // 4. Calculate translation delta
      final translationDeltaX = targetDx - currentTopLeft.dx;

      // If delta is negligible, do nothing
      if (translationDeltaX.abs() < 0.1) return;

      final translationDelta = Offset(translationDeltaX, 0);

      // 5. Apply translation to shape data
      ShapeData translatedShapeData;
      if (currentData is GroupShapeData) {
        final groupData = currentData;
        final newVertices =
            groupData.vertices.map((v) => v + translationDelta).toList();
        final newCenter = groupData.center + translationDelta;
        final updatedChildShapes =
            groupData.transformChildShapes(translation: translationDelta);
        final newGroupData = groupData.copyWith(
                vertices: newVertices,
                center: newCenter,
                boundingRect: groupData.boundingRect.shift(translationDelta))
            as GroupShapeData;
        translatedShapeData =
            newGroupData.copyWithChildren(childShapes: updatedChildShapes);
      } else {
        final newVertices =
            currentData.vertices.map((v) => v + translationDelta).toList();
        final newCenter = currentData.center + translationDelta;
        translatedShapeData = currentData.copyWith(
            vertices: newVertices,
            center: newCenter,
            boundingRect: currentData.boundingRect.shift(translationDelta));
      }

      // 6. Apply constraints
      final boardSize = _getBoardSize();
      final boundaryConstrainedData =
          _constrainShapeWithinBoundaries(translatedShapeData, boardSize);

      ShapeData finalConstrainedData = boundaryConstrainedData;
      if (widget.controller.isMirrorModeActive.value) {
        final mirrorConstrainedData = widget.controller
            .constrainShapePosition(widget.shapeKey!, boundaryConstrainedData);
        if (mirrorConstrainedData != null) {
          finalConstrainedData = mirrorConstrainedData;
        }
      }

      // 7. Save state WITHOUT history tracking
      widget.controller.saveShapeState(widget.shapeKey!, finalConstrainedData);

      // 8. Force UI update - DO NOT update text field here
      _forceRebuildShape(widget.shapeKey!, finalConstrainedData);
    } catch (e, stackTrace) {
      debugPrint("Error updating shape position real-time: $e\n$stackTrace");
      if (mounted && _needlePositionError == null) {
        setState(() {
          _needlePositionError =
              'shapeEditor_propertyHud_errors_positionGeneric'.tr;
        });
      }
    }
  }

  /// Update shape row position realtime
  void _updateShapeRowPositionRealtime(int rowNumber) {
    try {
      if (widget.shapeKey == null) return;

      // Get current shape data
      final currentData =
          widget.controller.getShapeState(widget.shapeKey)?.deepCopy();
      if (currentData == null) return;

      // Get current bounding box
      final dynamic boundsResult =
          GeometryUtils.calculateAccurateBoundingRect(currentData);
      Rect currentRect;
      if (boundsResult is GroupBoundsData) {
        currentRect = boundsResult.bounds;
      } else if (boundsResult is Rect) {
        currentRect = boundsResult;
      } else {
        return;
      }

      final currentTopLeft = currentRect.topLeft;

      // Get grid properties
      final gridSystem = widget.controller.gridSystem;
      final cellWidth = gridSystem.cellWidth;
      final aspectRatio = widget.controller.aspectRatio.value;
      final cellHeight = cellWidth * aspectRatio;

      if (cellHeight <= 0) return;

      // Calculate current row (1-based)
      final currentRow = (currentTopLeft.dy / cellHeight).round() + 1;

      // If already at target row, no change needed
      if (currentRow == rowNumber) return;

      // Calculate target Y position (convert from 1-based to 0-based for calculation)
      final targetY = (rowNumber - 1) * cellHeight;

      // Calculate vertical translation delta
      final deltaY = targetY - currentTopLeft.dy;

      // If delta is negligible, do nothing
      if (deltaY.abs() < 0.1) return;

      final translationDelta = Offset(0, deltaY);

      // Apply translation to shape data
      ShapeData translatedShapeData;
      if (currentData is GroupShapeData) {
        final groupData = currentData;
        final newVertices =
            groupData.vertices.map((v) => v + translationDelta).toList();
        final newCenter = groupData.center + translationDelta;
        final updatedChildShapes =
            groupData.transformChildShapes(translation: translationDelta);
        final newGroupData = groupData.copyWith(
                vertices: newVertices,
                center: newCenter,
                boundingRect: groupData.boundingRect.shift(translationDelta))
            as GroupShapeData;
        translatedShapeData =
            newGroupData.copyWithChildren(childShapes: updatedChildShapes);
      } else {
        final newVertices =
            currentData.vertices.map((v) => v + translationDelta).toList();
        final newCenter = currentData.center + translationDelta;
        translatedShapeData = currentData.copyWith(
            vertices: newVertices,
            center: newCenter,
            boundingRect: currentData.boundingRect.shift(translationDelta));
      }

      // Apply constraints
      final boardSize = _getBoardSize();
      final boundaryConstrainedData =
          _constrainShapeWithinBoundaries(translatedShapeData, boardSize);

      ShapeData finalConstrainedData = boundaryConstrainedData;
      if (widget.controller.isMirrorModeActive.value) {
        final mirrorConstrainedData = widget.controller
            .constrainShapePosition(widget.shapeKey!, boundaryConstrainedData);
        if (mirrorConstrainedData != null) {
          finalConstrainedData = mirrorConstrainedData;
        }
      }

      // Save state WITHOUT history tracking
      widget.controller.saveShapeState(widget.shapeKey!, finalConstrainedData);

      // Force UI update - DO NOT update text field here
      _forceRebuildShape(widget.shapeKey!, finalConstrainedData);
    } catch (e, stackTrace) {
      debugPrint(
          "Error updating shape row position real-time: $e\n$stackTrace");
      if (mounted && _rowPositionError == null) {
        setState(() {
          _rowPositionError =
              'shapeEditor_propertyHud_errors_positionGeneric'.tr;
        });
      }
    }
  }

  /// Handle needle input completion
  void _handleNeedleInputCompletion({bool trackHistory = true}) {
    final inputText =
        _needleController.text.toUpperCase(); // Use uppercase for parsing
    final gridSystem = widget.controller.gridSystem;
    final needleCount = gridSystem.needleCount;

    // Validate format before parsing
    if (!_isNeedleFormatValid(inputText)) {
      setState(() {
        _needlePositionError =
            'shapeEditor_propertyHud_errors_invalidNeedleFormat'.tr;
      });
      // Revert text field to last valid known position, force update even if focused
      _updateNeedlePositionDisplay(forceUpdateText: true);
      // If history was started but ended invalidly, ensure it's finished.
      if (trackHistory) widget.controller.finishHistoryTracking();
      return;
    }

    // Parse the input string (we know it's valid format now)
    final targetIndexZeroBased = _parseNeedleString(inputText, needleCount);

    if (targetIndexZeroBased != null) {
      // Valid format, clear error
      // Clear format/generic errors, leave constraint errors if present
      if (_needlePositionError ==
              'shapeEditor_propertyHud_errors_invalidNeedleFormat'.tr ||
          _needlePositionError ==
              'shapeEditor_propertyHud_errors_positionGeneric'.tr) {
        setState(() {
          _needlePositionError = null;
        });
      }

      // In different editing modes, call different completion methods
      if (_isInVertexEditMode) {
        _handleVertexNeedleInputCompletion(targetIndexZeroBased,
            trackHistory: trackHistory);
      } else if (_isInEdgeEditMode) {
        _handleEdgeControlNeedleInputCompletion(targetIndexZeroBased,
            trackHistory: trackHistory);
      } else {
        _handleShapeNeedleInputCompletion(targetIndexZeroBased,
            trackHistory: trackHistory);
      }
    } else {
      // Should not happen if _isNeedleFormatValid passed, but handle defensively
      setState(() {
        _needlePositionError =
            'shapeEditor_propertyHud_errors_invalidNeedleFormat'.tr;
      });
      _updateNeedlePositionDisplay(forceUpdateText: true);
      if (trackHistory) widget.controller.finishHistoryTracking();
    }
  }

  /// Handle vertex needle input completion
  void _handleVertexNeedleInputCompletion(int targetIndexZeroBased,
      {bool trackHistory = true}) {
    // Implementation for vertex needle position completion
    if (trackHistory) {
      widget.controller.startHistoryTracking("Set Vertex Needle Position");
    }

    // Apply the final position and finish tracking
    if (trackHistory) {
      widget.controller.finishHistoryTracking();
    }
  }

  /// Handle edge control needle input completion
  void _handleEdgeControlNeedleInputCompletion(int targetIndexZeroBased,
      {bool trackHistory = true}) {
    // Implementation for edge control needle position completion
    if (trackHistory) {
      widget.controller
          .startHistoryTracking("Set Edge Control Needle Position");
    }

    // Apply the final position and finish tracking
    if (trackHistory) {
      widget.controller.finishHistoryTracking();
    }
  }

  /// Handle shape needle input completion
  void _handleShapeNeedleInputCompletion(int targetIndexZeroBased,
      {bool trackHistory = true}) {
    // Implementation for shape needle position completion
    if (trackHistory) {
      widget.controller.startHistoryTracking("Set Shape Needle Position");
    }

    // Apply the final position and finish tracking
    if (trackHistory) {
      widget.controller.finishHistoryTracking();
    }
  }

  /// Handle row input completion
  void _handleRowInputCompletion({bool trackHistory = true}) {
    final inputText = _rowController.text;

    // Validate format (must be a positive integer)
    int? rowNumber;
    try {
      rowNumber = int.parse(inputText);

      if (rowNumber < 1) {
        throw FormatException('Row number must be positive');
      }

      // Clear format error if parsed successfully
      if (_rowPositionError ==
          'shapeEditor_propertyHud_errors_invalidRowFormat'.tr) {
        setState(() {
          _rowPositionError = null;
        });
      }
    } catch (e) {
      // Invalid format error
      setState(() {
        _rowPositionError =
            'shapeEditor_propertyHud_errors_invalidRowFormat'.tr;
      });

      // Revert to last valid value
      _updateRowPositionDisplay(forceUpdateText: true);

      // Finish history if started
      if (trackHistory) widget.controller.finishHistoryTracking();
      return;
    }

    // In different editing modes, call different completion methods
    if (_isInVertexEditMode) {
      _handleVertexRowInputCompletion(rowNumber, trackHistory: trackHistory);
    } else if (_isInEdgeEditMode) {
      _handleEdgeControlRowInputCompletion(rowNumber,
          trackHistory: trackHistory);
    } else {
      _handleShapeRowInputCompletion(rowNumber, trackHistory: trackHistory);
    }
  }

  /// Handle vertex row input completion
  void _handleVertexRowInputCompletion(int rowNumber,
      {bool trackHistory = true}) {
    // Implementation for vertex row position completion
    if (trackHistory) {
      widget.controller.startHistoryTracking("Set Vertex Row Position");
    }

    // Apply the final position and finish tracking
    if (trackHistory) {
      widget.controller.finishHistoryTracking();
    }
  }

  /// Handle edge control row input completion
  void _handleEdgeControlRowInputCompletion(int rowNumber,
      {bool trackHistory = true}) {
    // Implementation for edge control row position completion
    if (trackHistory) {
      widget.controller.startHistoryTracking("Set Edge Control Row Position");
    }

    // Apply the final position and finish tracking
    if (trackHistory) {
      widget.controller.finishHistoryTracking();
    }
  }

  /// Handle shape row input completion
  void _handleShapeRowInputCompletion(int rowNumber,
      {bool trackHistory = true}) {
    // Implementation for shape row position completion
    if (trackHistory) {
      widget.controller.startHistoryTracking("Set Shape Row Position");
    }

    // Apply the final position and finish tracking
    if (trackHistory) {
      widget.controller.finishHistoryTracking();
    }
  }

  /// Handle rotation input completion
  void _handleRotationInputCompletion({bool trackHistory = true}) {
    final inputText = _rotationController.text;

    // Use the new decimal input helper for parsing rotation (can be negative)
    final rotationValidation = DecimalInputHelper.validateDecimalInput(
        inputText,
        requirePositive: false // Rotation can be negative
        );

    if (!rotationValidation.isValid) {
      // Invalid input
      setState(() {
        _rotationError = 'shapeEditor_propertyHud_errors_invalidRotation'.tr;
      });
      // Revert text field to the shape's current actual rotation
      final currentRotation = widget.shapeData?.visualRotation.round() ?? 0;
      // Check focus again before setting text to avoid race conditions
      if (!_rotationFocus.hasFocus) {
        _rotationController.text = currentRotation.toString();
      }
      // If history was started by focus gain but ended with invalid input, ensure it's finished/aborted.
      if (trackHistory) {
        widget.controller.finishHistoryTracking(); // Finish without changes
      }
      return;
    }

    // Clear rotation error if it was a parse error
    if (_rotationError == 'shapeEditor_propertyHud_errors_invalidRotation'.tr) {
      setState(() {
        _rotationError = null;
      });
    }

    // Convert to integer for rotation (as rotation is typically in whole degrees)
    final rotationDegrees = rotationValidation.value!.round();

    // Call _setRotation to apply the final rotation
    _setRotation(rotationDegrees, trackHistory: trackHistory);
  }

  /// Set rotation
  void _setRotation(int degrees, {bool trackHistory = true}) {
    try {
      if (widget.shapeKey == null) {
        if (trackHistory) widget.controller.finishHistoryTracking();
        return;
      }

      if (trackHistory) {
        widget.controller.startHistoryTracking("Set Rotation");
      }

      // Get current shape data FROM THE CONTROLLER to ensure it's the latest
      final currentData =
          widget.controller.getShapeState(widget.shapeKey)?.deepCopy();
      if (currentData == null) {
        debugPrint("Error in _setRotation: Could not get current shape state.");
        if (trackHistory) {
          widget.controller.finishHistoryTracking(); // Abort if started here
        }
        return; // Cannot proceed without current data
      }

      // Normalize degrees input
      int normalizedDegrees = degrees % 360;
      if (normalizedDegrees < 0) normalizedDegrees += 360;

      // Get the current rotation in degrees and calculate delta
      final currentDegrees =
          currentData.visualRotation.round(); // Already in degrees
      final deltaDegreesRaw = normalizedDegrees - currentDegrees;

      // Normalize delta to -180 to 180 range for shortest rotation path
      int deltaDegrees = deltaDegreesRaw % 360;
      if (deltaDegrees > 180) deltaDegrees -= 360;
      if (deltaDegrees < -180) deltaDegrees += 360;

      // If no change, exit
      if (deltaDegrees == 0) {
        // Even if no rotation, ensure text field is correct if not focused
        if (!_rotationFocus.hasFocus) {
          final roundedCurrent = currentData.visualRotation.round();
          if (_rotationController.text != roundedCurrent.toString()) {
            // Use WidgetsBinding to avoid setState during build/layout
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (mounted && !_rotationFocus.hasFocus) {
                // Re-check focus
                _rotationController.text = roundedCurrent.toString();
              }
            });
          }
        }
        if (trackHistory) {
          widget.controller.finishHistoryTracking(); // Finish if started here
        }
        return; // No rotation needed
      }

      // Convert the final delta degrees back to radians for the handler
      final rotationDelta = deltaDegrees * (math.pi / 180);

      // Calculate the positions needed for handleRotation
      final center = currentData.center;
      final radius = 10.0; // Use a small arbitrary radius
      final currentRotationRadians =
          currentData.visualRotation * (math.pi / 180);
      final lastPosition = Offset(
        center.dx + radius * math.cos(currentRotationRadians),
        center.dy + radius * math.sin(currentRotationRadians),
      );
      final newPosition = Offset(
        center.dx + radius * math.cos(currentRotationRadians + rotationDelta),
        center.dy + radius * math.sin(currentRotationRadians + rotationDelta),
      );

      // Get shape constraints
      int index =
          widget.controller.shapes.indexWhere((s) => s.key == widget.shapeKey);
      if (index < 0) {
        if (trackHistory) widget.controller.finishHistoryTracking();
        return;
      }
      final constraints = widget.controller.shapes[index].constraints;

      // Use the ShapeManipulationHandlers.handleRotation method
      final rotatedData = ShapeManipulationHandlers.handleRotation(
        shapeData: currentData,
        lastPosition: lastPosition,
        newPosition: newPosition,
        constraints: constraints,
      );

      // Check if rotation was prevented by constraints
      if ((rotatedData.visualRotation - currentData.visualRotation).abs() <
          0.01) {
        // Rotation was prevented, show error
        if (_rotationError !=
            'shapeEditor_propertyHud_errors_rotationPrevented'.tr) {
          setState(() {
            _rotationError =
                'shapeEditor_propertyHud_errors_rotationPrevented'.tr;
          });
        }
        // Update text field to match actual (unchanged) rotation ONLY if not focused
        if (!_rotationFocus.hasFocus) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted && !_rotationFocus.hasFocus) {
              // Re-check focus
              _rotationController.text = currentDegrees.toString();
            }
          });
        }
        if (trackHistory) {
          widget.controller
              .finishHistoryTracking(); // End tracking without changes
        }
        return;
      }

      // Rotation was successful, clear any previous rotation error
      if (_rotationError != null) {
        setState(() {
          _rotationError = null;
        });
      }

      // Save the rotated shape state
      // Let saveShapeState handle history based on controller.isTrackingHistory
      widget.controller.saveShapeState(widget.shapeKey!, rotatedData);

      if (trackHistory) {
        widget.controller
            .finishHistoryTracking(); // Finish history if started here
      }

      // Force rebuild the shape with new data
      _forceRebuildShape(widget.shapeKey!, rotatedData);

      // Update the rotation controller text AFTER saving and rebuilding
      final finalAppliedDegrees = rotatedData.visualRotation.round();
      // Update text ONLY if not focused to avoid interfering with typing
      if (!_rotationFocus.hasFocus) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          // Re-check focus state within the callback
          if (mounted && !_rotationFocus.hasFocus) {
            // Check if text actually needs changing
            if (_rotationController.text != finalAppliedDegrees.toString()) {
              _rotationController.text = finalAppliedDegrees.toString();
            }
          }
        });
      }
    } catch (e, stackTrace) {
      debugPrint("Error setting rotation: $e\n$stackTrace");
      if (mounted) {
        // Check mount status before setting state
        setState(() {
          _rotationError = 'Error applying rotation.';
        });
      }
      // Ensure history tracking ends on error if it was started here
      if (trackHistory) {
        // Simplified check
        widget.controller.finishHistoryTracking();
      }
    }
  }

  /// Helper function to validate needle format strictly
  bool _isNeedleFormatValid(String value) {
    if (value.isEmpty) return true; // Allow empty
    final regex = RegExp(r'^(?:[LR]\d+|0)$', caseSensitive: false);
    return regex.hasMatch(value);
  }

  /// Snap active shape to center
  void snapActiveShapeToCenter() {
    // Disable snapping in mirror mode
    if (widget.controller.isMirrorModeActive.value) {
      Get.snackbar(
        'shapeEditor_propertyHud_mirrorMode_active'.tr,
        'shapeEditor_propertyHud_snapDisabled'.tr,
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 2),
      );
      return;
    }

    try {
      if (widget.shapeKey == null || widget.shapeData == null) return;

      // 1. Get current shape data and key
      final shapeKey = widget.shapeKey!;
      final currentData = widget.controller.getShapeState(shapeKey)?.deepCopy();
      if (currentData == null) return;

      // 2. Calculate accurate bounding box
      final dynamic boundsResult =
          GeometryUtils.calculateAccurateBoundingRect(currentData);
      Rect currentRect;
      if (boundsResult is GroupBoundsData) {
        currentRect = boundsResult.bounds;
      } else if (boundsResult is Rect) {
        currentRect = boundsResult;
      } else {
        return; // Error getting bounds
      }
      final currentShapeCenterX = currentRect.center.dx;

      // 3. Determine board center X (independent of zoom/pan for horizontal centering)
      final boardCenterX = widget.screenSize?.width != null
          ? widget.screenSize!.width / 2
          : Get.width / 2;

      // 4. Calculate horizontal translation delta
      final deltaX = boardCenterX - currentShapeCenterX;

      // If already centered (or very close), do nothing
      if (deltaX.abs() < 0.5) {
        return;
      }

      final translationDelta = Offset(deltaX, 0);

      // Start history tracking
      widget.controller.startHistoryTracking("Snap to Center");

      // 5. Apply translation to the shape data
      ShapeData translatedShapeData;
      if (currentData is GroupShapeData) {
        final groupData = currentData;
        // Translate group vertices and center
        final newVertices =
            groupData.vertices.map((v) => v + translationDelta).toList();
        final newCenter = groupData.center + translationDelta;
        // Translate child shapes efficiently
        final updatedChildShapes = groupData.transformChildShapes(
          translation: translationDelta,
        );
        // Create new group data
        final newGroupData = groupData.copyWith(
          vertices: newVertices,
          center: newCenter,
          boundingRect: groupData.boundingRect.shift(translationDelta),
        ) as GroupShapeData;
        translatedShapeData = newGroupData.copyWithChildren(
          childShapes: updatedChildShapes,
        );
      } else {
        // Regular shape translation
        final newVertices =
            currentData.vertices.map((v) => v + translationDelta).toList();
        final newCenter = currentData.center + translationDelta;
        translatedShapeData = currentData.copyWith(
          vertices: newVertices,
          center: newCenter,
          boundingRect: currentData.boundingRect.shift(translationDelta),
        );
      }

      // 6. Use the translated data directly (no boundary constraints for snap)
      final finalConstrainedData = translatedShapeData;

      // 7. Save state
      widget.controller.saveShapeState(shapeKey, finalConstrainedData);
      widget.controller.finishHistoryTracking();

      // 8. Force UI update (shape and HUD display)
      _forceRebuildShape(shapeKey, finalConstrainedData);
      _updateNeedlePositionDisplay(); // Update HUD text immediately
    } catch (e, stackTrace) {
      debugPrint("Error snapping shape to center: $e\n$stackTrace");
      widget.controller
          .finishHistoryTracking(); // Ensure history tracking ends on error
    }
  }

  // --- Updated Method to Update Row Position Display ---
  void _updateRowPositionDisplay({bool forceUpdateText = false}) {
    try {
      // Get the latest shape data from controller if possible
      final shapeData = widget.shapeKey != null
          ? widget.controller.getShapeState(widget.shapeKey)
          : widget.shapeData;

      if (shapeData == null) {
        _rowPositionString = "1";
        if (mounted) setState(() {});
        return;
      }

      // Check if we're in vertex edit mode
      if (_isInVertexEditMode) {
        _updateVertexRowPositionDisplay(shapeData,
            forceUpdateText: forceUpdateText);
        return;
      }

      // Check if we're in edge edit mode
      if (_isInEdgeEditMode) {
        _updateEdgeControlRowPositionDisplay(shapeData,
            forceUpdateText: forceUpdateText);
        return;
      }

      // Default mode: show whole shape position
      // 1. Get current shape data
      final currentData = shapeData;

      // 2. Calculate accurate bounding box
      final dynamic boundsResult =
          GeometryUtils.calculateAccurateBoundingRect(currentData);
      Rect rect;
      if (boundsResult is GroupBoundsData) {
        rect = boundsResult.bounds;
      } else if (boundsResult is Rect) {
        rect = boundsResult;
      } else {
        _rowPositionString = "1"; // Error case
        if (mounted) setState(() {});
        return;
      }

      // 3. Get top-left corner
      final topLeftOffset = rect.topLeft;

      // 4. Convert to row number using grid properties
      final gridSystem = widget.controller.gridSystem;
      final cellWidth = gridSystem.cellWidth;
      final aspectRatio = widget.controller.aspectRatio.value;
      final cellHeight = cellWidth * aspectRatio;

      // Ensure cellHeight is positive to avoid division by zero
      if (cellHeight <= 0) {
        _rowPositionString = "1";
        if (mounted) setState(() {});
        return;
      }

      // Calculate the 1-based row index (closest row to the top edge)
      final rowNumber = (topLeftOffset.dy / cellHeight).round() + 1;

      // Ensure positive value
      _rowPositionString = math.max(1, rowNumber).toString();

      // Update the controller text ONLY if not focused OR forceUpdate is true
      if (forceUpdateText || !_rowFocus.hasFocus) {
        // Store current selection only if focused (and not forced)
        TextSelection? currentSelection =
            _rowFocus.hasFocus ? _rowController.selection : null;

        // Check if text actually needs changing
        if (_rowController.text != _rowPositionString) {
          _rowController.text = _rowPositionString;
        }

        // Restore selection if it was valid and we were focused
        if (_rowFocus.hasFocus && currentSelection != null) {
          try {
            // Ensure selection is within new text bounds
            final start =
                math.min(currentSelection.start, _rowController.text.length);
            final end =
                math.min(currentSelection.end, _rowController.text.length);
            _rowController.selection =
                TextSelection(baseOffset: start, extentOffset: end);
          } catch (e) {/* Ignore selection errors */}
        }
      }

      // Update state only if mounted
      if (mounted) {
        // Only call setState if the string actually changed
        // setState(() {}); <-- Removed redundant setState here
      }
    } catch (e, stackTrace) {
      debugPrint("Error calculating row position: $e\n$stackTrace");
      _rowPositionString = "1";
      if (mounted) {
        setState(() {});
      }
    }
  }

  // --- Method to adjust rotation incrementally ---
  void _adjustRotation(int deltaDegrees) {
    try {
      if (!_areRotationControlsEnabled) return;
      if (widget.shapeKey == null) return;

      widget.controller.startHistoryTracking("Adjust Rotation");

      // Get current shape data FROM THE CONTROLLER to ensure it's the latest
      final currentData =
          widget.controller.getShapeState(widget.shapeKey)?.deepCopy();
      if (currentData == null) {
        widget.controller.finishHistoryTracking();
        return;
      }

      // Get the current rotation in degrees and calculate target
      final currentDegrees = currentData.visualRotation.round();
      final targetDegrees = currentDegrees + deltaDegrees;

      // Call _setRotation to apply the final rotation
      _setRotation(targetDegrees, trackHistory: false);

      // Finish history tracking here
      widget.controller.finishHistoryTracking();
    } catch (e, stackTrace) {
      debugPrint("Error adjusting rotation: $e\n$stackTrace");
      widget.controller.finishHistoryTracking();
    }
  }

  /// Update shape rotation realtime (from onChanged)
  void _updateShapeRotationRealtime(int rotationDegrees) {
    try {
      if (!_areRotationControlsEnabled) return;
      if (widget.shapeKey == null) return;

      // Get current shape data FROM THE CONTROLLER to ensure it's the latest
      final currentData =
          widget.controller.getShapeState(widget.shapeKey)?.deepCopy();
      if (currentData == null) return;

      // Normalize degrees input
      int normalizedDegrees = rotationDegrees % 360;
      if (normalizedDegrees < 0) normalizedDegrees += 360;

      // Get the current rotation in degrees and calculate delta
      final currentDegrees = currentData.visualRotation.round();
      final deltaDegreesRaw = normalizedDegrees - currentDegrees;

      // Normalize delta to -180 to 180 range for shortest rotation path
      int deltaDegrees = deltaDegreesRaw % 360;
      if (deltaDegrees > 180) deltaDegrees -= 360;
      if (deltaDegrees < -180) deltaDegrees += 360;

      // If no change, exit
      if (deltaDegrees == 0) return;

      // Convert the final delta degrees back to radians for the handler
      final rotationDelta = deltaDegrees * (math.pi / 180);

      // Calculate the positions needed for handleRotation
      final center = currentData.center;
      final radius = 10.0; // Use a small arbitrary radius
      final currentRotationRadians =
          currentData.visualRotation * (math.pi / 180);
      final lastPosition = Offset(
        center.dx + radius * math.cos(currentRotationRadians),
        center.dy + radius * math.sin(currentRotationRadians),
      );
      final newPosition = Offset(
        center.dx + radius * math.cos(currentRotationRadians + rotationDelta),
        center.dy + radius * math.sin(currentRotationRadians + rotationDelta),
      );

      // Get shape constraints
      int index =
          widget.controller.shapes.indexWhere((s) => s.key == widget.shapeKey);
      if (index < 0) return;
      final constraints = widget.controller.shapes[index].constraints;

      // Use the ShapeManipulationHandlers.handleRotation method
      final rotatedData = ShapeManipulationHandlers.handleRotation(
        shapeData: currentData,
        lastPosition: lastPosition,
        newPosition: newPosition,
        constraints: constraints,
      );

      // Save state WITHOUT history tracking (real-time update)
      widget.controller.saveShapeState(widget.shapeKey!, rotatedData);

      // Force rebuild the shape with new data
      _forceRebuildShape(widget.shapeKey!, rotatedData);
    } catch (e, stackTrace) {
      debugPrint("Error updating shape rotation real-time: $e\n$stackTrace");
      if (mounted && _rotationError == null) {
        setState(() {
          _rotationError = 'shapeEditor_propertyHud_errors_positionGeneric'.tr;
        });
      }
    }
  }

  /// Set row position
  void _setRowPosition(int rowNumber, {bool trackHistory = true}) {
    try {
      if (widget.shapeKey == null) {
        if (trackHistory) widget.controller.finishHistoryTracking();
        return;
      }

      if (trackHistory) {
        widget.controller.startHistoryTracking("Set Row Position");
      }

      // Get current shape data FROM THE CONTROLLER to ensure it's the latest
      final currentData =
          widget.controller.getShapeState(widget.shapeKey)?.deepCopy();
      if (currentData == null) {
        if (trackHistory) widget.controller.finishHistoryTracking();
        return;
      }

      // Get current bounding box
      final dynamic boundsResult =
          GeometryUtils.calculateAccurateBoundingRect(currentData);
      Rect currentRect;
      if (boundsResult is GroupBoundsData) {
        currentRect = boundsResult.bounds;
      } else if (boundsResult is Rect) {
        currentRect = boundsResult;
      } else {
        if (trackHistory) widget.controller.finishHistoryTracking();
        return;
      }

      final currentTopLeft = currentRect.topLeft;

      // Get grid properties
      final gridSystem = widget.controller.gridSystem;
      final cellWidth = gridSystem.cellWidth;
      final aspectRatio = widget.controller.aspectRatio.value;
      final cellHeight = cellWidth * aspectRatio;

      if (cellHeight <= 0) {
        if (trackHistory) widget.controller.finishHistoryTracking();
        return;
      }

      // Calculate target Y position (convert from 1-based to 0-based for calculation)
      final targetY = (rowNumber - 1) * cellHeight;

      // Calculate vertical translation delta
      final deltaY = targetY - currentTopLeft.dy;

      // If delta is negligible, no change needed
      if (deltaY.abs() < 0.1) {
        if (trackHistory) widget.controller.finishHistoryTracking();
        return;
      }

      final translationDelta = Offset(0, deltaY);

      // Apply translation to shape data
      ShapeData translatedShapeData;
      if (currentData is GroupShapeData) {
        final groupData = currentData;
        final newVertices =
            groupData.vertices.map((v) => v + translationDelta).toList();
        final newCenter = groupData.center + translationDelta;
        final updatedChildShapes =
            groupData.transformChildShapes(translation: translationDelta);
        final newGroupData = groupData.copyWith(
                vertices: newVertices,
                center: newCenter,
                boundingRect: groupData.boundingRect.shift(translationDelta))
            as GroupShapeData;
        translatedShapeData =
            newGroupData.copyWithChildren(childShapes: updatedChildShapes);
      } else {
        final newVertices =
            currentData.vertices.map((v) => v + translationDelta).toList();
        final newCenter = currentData.center + translationDelta;
        translatedShapeData = currentData.copyWith(
            vertices: newVertices,
            center: newCenter,
            boundingRect: currentData.boundingRect.shift(translationDelta));
      }

      // Apply constraints
      final boardSize = _getBoardSize();
      final boundaryConstrainedData =
          _constrainShapeWithinBoundaries(translatedShapeData, boardSize);

      ShapeData finalConstrainedData = boundaryConstrainedData;
      if (widget.controller.isMirrorModeActive.value) {
        final mirrorConstrainedData = widget.controller
            .constrainShapePosition(widget.shapeKey!, boundaryConstrainedData);
        if (mirrorConstrainedData != null) {
          finalConstrainedData = mirrorConstrainedData;
        }
      }

      // Save state
      widget.controller.saveShapeState(widget.shapeKey!, finalConstrainedData);

      if (trackHistory) {
        widget.controller.finishHistoryTracking();
      }

      // Force UI update
      _forceRebuildShape(widget.shapeKey!, finalConstrainedData);

      // Update HUD display if not focused
      if (!_rowFocus.hasFocus) {
        _updateRowPositionDisplay();
      }
    } catch (e, stackTrace) {
      debugPrint("Error setting row position: $e\n$stackTrace");
      if (trackHistory) {
        widget.controller.finishHistoryTracking();
      }
    }
  }
}
