import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:get/get.dart';
import 'package:xoxknit/app/modules/shape_test/models/shape_data.dart';
import 'package:xoxknit/app/modules/shape_test/controllers/shape_editor_controller.dart';

void main() {
  group('Active Handle Visual Feedback Tests', () {
    late ShapeEditorController controller;

    setUp(() {
      TestWidgetsFlutterBinding.ensureInitialized();
      Get.testMode = true;
      controller = ShapeEditorController();
    });

    tearDown(() {
      Get.reset();
    });

    test('should set active handle correctly', () {
      final shapeKey = GlobalKey();

      // Test setting vertex handle as active
      controller.setActiveHandle(HandleType.vertex, shapeKey, index: 0);

      expect(controller.activeHandle.value, isNotNull);
      expect(controller.activeHandle.value!.type, equals(HandleType.vertex));
      expect(controller.activeHandle.value!.shapeKey, equals(shapeKey));
      expect(controller.activeHandle.value!.index, equals(0));
    });

    test('should clear active handle correctly', () {
      final shapeKey = GlobalKey();

      // Set an active handle first
      controller.setActiveHandle(HandleType.edge, shapeKey, index: 1);
      expect(controller.activeHandle.value, isNotNull);

      // Clear it
      controller.clearActiveHandle();
      expect(controller.activeHandle.value, isNull);
    });

    test('should check handle active state correctly', () {
      final shapeKey = GlobalKey();

      // Set a cubic bezier control handle as active
      controller.setActiveHandle(HandleType.cubicBezierControl, shapeKey,
          index: 2, subIndex: 1);

      // Set to side edit mode (required for visual feedback)
      controller.currentHandleMode.value = HandleMode.sideEdit;

      // Check if the same handle is active
      expect(
          controller.isHandleActive(HandleType.cubicBezierControl, shapeKey,
              index: 2, subIndex: 1),
          isTrue);

      // Check if a different handle is active
      expect(
          controller.isHandleActive(HandleType.cubicBezierControl, shapeKey,
              index: 2, subIndex: 0),
          isFalse);

      // Check if a different type is active
      expect(controller.isHandleActive(HandleType.vertex, shapeKey, index: 2),
          isFalse);

      // Check that in normal mode, no handles show as active
      controller.currentHandleMode.value = HandleMode.normal;
      expect(
          controller.isHandleActive(HandleType.cubicBezierControl, shapeKey,
              index: 2, subIndex: 1),
          isFalse,
          reason: 'Handles should not show as active in normal mode');
    });

    test('should handle ActiveHandleInfo equality correctly', () {
      final shapeKey1 = GlobalKey();
      final shapeKey2 = GlobalKey();

      final handle1 = ActiveHandleInfo(
        type: HandleType.vertex,
        shapeKey: shapeKey1,
        index: 0,
      );

      final handle2 = ActiveHandleInfo(
        type: HandleType.vertex,
        shapeKey: shapeKey1,
        index: 0,
      );

      final handle3 = ActiveHandleInfo(
        type: HandleType.vertex,
        shapeKey: shapeKey2,
        index: 0,
      );

      expect(handle1, equals(handle2));
      expect(handle1, isNot(equals(handle3)));
    });

    test('should clear active handle manually', () {
      final shapeKey = GlobalKey();

      // Set an active handle
      controller.setActiveHandle(HandleType.vertex, shapeKey, index: 0);
      expect(controller.activeHandle.value, isNotNull);

      // Manually clear active handle (simulating what happens on drag end)
      controller.clearActiveHandle();

      // Active handle should be cleared
      expect(controller.activeHandle.value, isNull);
    });

    test('should clear active handle when exiting handle modes', () {
      final shapeKey = GlobalKey();

      // Set the controller to a special handle mode first
      controller.currentHandleMode.value = HandleMode.sideEdit;
      controller.activeHandleModeShapeKey.value = shapeKey;

      // Set an active handle
      controller.setActiveHandle(HandleType.cubicBezierControl, shapeKey,
          index: 0, subIndex: 0);
      expect(controller.activeHandle.value, isNotNull);

      // Exit current handle mode
      controller.exitCurrentHandleMode();

      // Active handle should be cleared
      expect(controller.activeHandle.value, isNull);
    });
  });
}
